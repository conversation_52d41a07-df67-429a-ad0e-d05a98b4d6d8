# Setup Scripts

This directory contains automated setup scripts for the Neuronic Lab Laravel application.

## Available Scripts

### setup.sh (Unix/Linux/macOS)

Automated setup script for Unix-based systems including Linux and macOS.

**Usage:**
```bash
./scripts/setup.sh
```

**What it does:**
- Creates `.env` file from `.env.example` if it doesn't exist
- Installs Composer dependencies (`composer install` or `composer update`)
- Installs NPM dependencies (`npm install` or `npm update`)
- Generates Laravel application key (`php artisan key:generate`)
- Creates storage symlink (`php artisan storage:link`)
- Sets proper file permissions for `storage` and `bootstrap/cache`
- Builds frontend assets (`npm run dev`)
- Displays next steps and helpful information

**Requirements:**
- PHP 8.2+
- Composer
- Node.js & NPM
- MySQL (for database setup)

### setup.bat (Windows)

Automated setup script for Windows systems.

**Usage:**
```batch
scripts\setup.bat
```

**What it does:**
- Same functionality as `setup.sh` but with Windows-compatible commands
- Uses Windows batch file syntax
- Includes `pause` command at the end to keep window open

**Requirements:**
- PHP 8.2+
- Composer
- Node.js & NPM
- MySQL (for database setup)

## Post-Setup Steps

After running either script, you'll need to:

1. **Configure Database:**
   - Update database credentials in `.env`
   - Run `php artisan migrate` to create database tables

2. **Configure Mail (Optional):**
   - Update SMTP settings in `.env`
   - Test with contact forms

3. **Configure reCAPTCHA (Optional):**
   - Get keys from [Google reCAPTCHA](https://www.google.com/recaptcha/admin)
   - Update `NOCAPTCHA_SECRET` and `NOCAPTCHA_SITEKEY` in `.env`

4. **Start Development Server:**
   ```bash
   php artisan serve
   ```

## Troubleshooting

### Permission Issues (Unix/Linux/macOS)
If you get permission denied errors:
```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### Missing Dependencies
If Composer or NPM are not installed:
- **Composer**: [Download from getcomposer.org](https://getcomposer.org/download/)
- **Node.js/NPM**: [Download from nodejs.org](https://nodejs.org/)

### Database Connection Issues
- Ensure MySQL is running
- Verify database credentials in `.env`
- Create the database if it doesn't exist

## Manual Setup Alternative

If you prefer manual setup or the scripts don't work in your environment, see the [Environment Setup Guide](../docs/environment-setup.md) for detailed manual instructions.

## Contributing

When adding new setup scripts:
1. Follow the existing naming convention
2. Include proper error handling
3. Add documentation to this README
4. Test on the target platform
5. Update the main project README if needed
