<?php
/**
 * <PERSON><PERSON> Auto-Fix Script for cPanel/Shared Hosting
 * Upload this to your public directory and run it once
 * DELETE THIS FILE AFTER USE FOR SECURITY!
 */

// Prevent caching
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

echo "<!DOCTYPE html>
<html>
<head>
    <title>Laravel Auto-Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .delete-warning { background: #ffebee; padding: 15px; border: 2px solid red; margin: 20px 0; }
        .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
    </style>
</head>
<body>";

echo "<h1>🔧 Laravel Auto-Fix Script</h1>";
echo "<p><strong>Site:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Function to display status
function displayStatus($condition, $message) {
    $class = $condition ? 'success' : 'error';
    $icon = $condition ? '✅' : '❌';
    echo "<div class='$class'>$icon $message</div>";
    return $condition;
}

function displayInfo($message) {
    echo "<div class='info'>ℹ️ $message</div>";
}

// Determine Laravel root
$laravelRoot = dirname(__FILE__);
$isInPublic = basename($laravelRoot) === 'public';
if ($isInPublic) {
    $laravelRoot = dirname($laravelRoot);
}

// Check if we should run fixes
$runFixes = isset($_GET['run']) && $_GET['run'] === 'true';

if (!$runFixes) {
    echo "<div class='section'>";
    echo "<h2>Ready to Fix Issues</h2>";
    echo "<p>This script will attempt to fix the following issues:</p>";
    echo "<ul>";
    echo "<li>Generate missing APP_KEY</li>";
    echo "<li>Create storage symlink</li>";
    echo "<li>Fix directory permissions</li>";
    echo "<li>Clear and rebuild caches</li>";
    echo "<li>Update .env file with basic database settings</li>";
    echo "</ul>";
    echo "<a href='?run=true' class='button'>🚀 Run Auto-Fix</a>";
    echo "</div>";
} else {
    echo "<div class='section'>";
    echo "<h2>Running Auto-Fix...</h2>";
    
    try {
        // Load Laravel
        require_once "$laravelRoot/vendor/autoload.php";
        $app = require_once "$laravelRoot/bootstrap/app.php";
        $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
        
        displayStatus(true, "Laravel loaded successfully");
        
        // Fix 1: Generate APP_KEY
        echo "<h3>1. Generating APP_KEY</h3>";
        try {
            $kernel->call('key:generate', ['--force' => true]);
            displayStatus(true, "APP_KEY generated successfully");
        } catch (Exception $e) {
            displayStatus(false, "Failed to generate APP_KEY: " . $e->getMessage());
            
            // Manual APP_KEY generation
            echo "<div class='info'>Attempting manual APP_KEY generation...</div>";
            $key = 'base64:' . base64_encode(random_bytes(32));
            
            // Update .env file
            $envFile = "$laravelRoot/.env";
            if (file_exists($envFile)) {
                $envContent = file_get_contents($envFile);
                if (strpos($envContent, 'APP_KEY=') !== false) {
                    $envContent = preg_replace('/APP_KEY=.*/', "APP_KEY=$key", $envContent);
                } else {
                    $envContent .= "\nAPP_KEY=$key\n";
                }
                file_put_contents($envFile, $envContent);
                displayStatus(true, "APP_KEY manually added to .env file");
            }
        }
        
        // Fix 2: Create storage symlink
        echo "<h3>2. Creating Storage Symlink</h3>";
        try {
            $kernel->call('storage:link');
            displayStatus(true, "Storage symlink created successfully");
        } catch (Exception $e) {
            displayStatus(false, "Failed to create storage symlink: " . $e->getMessage());
            
            // Manual symlink creation
            $publicStorage = $isInPublic ? 'storage' : "$laravelRoot/public/storage";
            $storagePublic = "$laravelRoot/storage/app/public";
            
            if (!file_exists($publicStorage) && is_dir($storagePublic)) {
                if (symlink($storagePublic, $publicStorage)) {
                    displayStatus(true, "Storage symlink created manually");
                } else {
                    displayStatus(false, "Failed to create symlink manually");
                }
            }
        }
        
        // Fix 3: Clear caches
        echo "<h3>3. Clearing Caches</h3>";
        try {
            $kernel->call('optimize:clear');
            displayStatus(true, "All caches cleared successfully");
        } catch (Exception $e) {
            displayStatus(false, "Failed to clear caches: " . $e->getMessage());
        }
        
        // Fix 4: Fix permissions (limited in shared hosting)
        echo "<h3>4. Fixing Permissions</h3>";
        $directories = [
            "$laravelRoot/storage/logs",
            "$laravelRoot/storage/framework/cache",
            "$laravelRoot/storage/framework/sessions",
            "$laravelRoot/storage/framework/views",
            "$laravelRoot/bootstrap/cache"
        ];
        
        foreach ($directories as $dir) {
            if (is_dir($dir)) {
                if (chmod($dir, 0755)) {
                    displayStatus(true, "Fixed permissions for " . basename($dir));
                } else {
                    displayStatus(false, "Could not fix permissions for " . basename($dir));
                }
            } else {
                // Create directory if it doesn't exist
                if (mkdir($dir, 0755, true)) {
                    displayStatus(true, "Created directory " . basename($dir));
                }
            }
        }
        
        // Fix 5: Update .env with basic database settings
        echo "<h3>5. Updating .env File</h3>";
        $envFile = "$laravelRoot/.env";
        if (file_exists($envFile)) {
            $envContent = file_get_contents($envFile);
            
            // Add missing database settings if they don't exist
            $dbSettings = [
                'DB_CONNECTION' => 'mysql',
                'DB_HOST' => '127.0.0.1',
                'DB_PORT' => '3306',
                'DB_DATABASE' => 'your_database_name',
                'DB_USERNAME' => 'your_username',
                'DB_PASSWORD' => 'your_password'
            ];
            
            $updated = false;
            foreach ($dbSettings as $key => $value) {
                if (strpos($envContent, "$key=") === false) {
                    $envContent .= "\n$key=$value\n";
                    $updated = true;
                }
            }
            
            if ($updated) {
                file_put_contents($envFile, $envContent);
                displayStatus(true, "Added missing database settings to .env");
                echo "<div class='warning'>⚠️ Please update the database settings in .env with your actual values!</div>";
            } else {
                displayStatus(true, "Database settings already exist in .env");
            }
        }
        
        // Fix 6: Rebuild caches
        echo "<h3>6. Rebuilding Caches</h3>";
        try {
            $kernel->call('config:cache');
            displayStatus(true, "Configuration cached");
        } catch (Exception $e) {
            displayStatus(false, "Failed to cache config: " . $e->getMessage());
        }
        
        try {
            $kernel->call('route:cache');
            displayStatus(true, "Routes cached");
        } catch (Exception $e) {
            displayStatus(false, "Failed to cache routes: " . $e->getMessage());
        }
        
        try {
            $kernel->call('view:cache');
            displayStatus(true, "Views cached");
        } catch (Exception $e) {
            displayStatus(false, "Failed to cache views: " . $e->getMessage());
        }
        
    } catch (Exception $e) {
        displayStatus(false, "Critical error: " . $e->getMessage());
    }
    
    echo "</div>";
    
    // Next steps
    echo "<div class='section'>";
    echo "<h2>✅ Auto-Fix Complete!</h2>";
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Delete this autofix.php file immediately!</strong></li>";
    echo "<li>Update your .env file with correct database credentials via cPanel File Manager</li>";
    echo "<li>Test your website: <a href='/' target='_blank'>Visit Homepage</a></li>";
    echo "<li>If still having issues, check the error logs in cPanel</li>";
    echo "</ol>";
    echo "</div>";
}

// Security Warning
echo "<div class='delete-warning'>";
echo "<h2>🚨 SECURITY WARNING</h2>";
echo "<p><strong>DELETE THIS FILE IMMEDIATELY AFTER USE!</strong></p>";
echo "<p>This script can modify your Laravel application and should not remain on your server.</p>";
echo "<p>File location: " . __FILE__ . "</p>";
echo "</div>";

echo "</body></html>";
?>
