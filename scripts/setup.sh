#!/bin/bash

# Neuronic Lab Laravel Application Setup Script
# This script helps set up the development environment quickly

echo "🚀 Setting up Neuronic Lab Laravel Application..."
echo "=================================================="

# Check if .env exists
if [ ! -f .env ]; then
    echo "📋 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created"
else
    echo "⚠️  .env file already exists, skipping..."
fi

# Install Composer dependencies
if [ ! -d "vendor" ]; then
    echo "📦 Installing Composer dependencies..."
    composer install
    echo "✅ Composer dependencies installed"
else
    echo "⚠️  Vendor directory exists, running composer update..."
    composer update
fi

# Install NPM dependencies
if [ ! -d "node_modules" ]; then
    echo "📦 Installing NPM dependencies..."
    npm install
    echo "✅ NPM dependencies installed"
else
    echo "⚠️  Node modules exist, running npm update..."
    npm update
fi

# Generate application key
echo "🔑 Generating application key..."
php artisan key:generate
echo "✅ Application key generated"

# Create storage symlink
echo "🔗 Creating storage symlink..."
php artisan storage:link
echo "✅ Storage symlink created"

# Set permissions
echo "🔒 Setting proper permissions..."
chmod -R 775 storage bootstrap/cache
echo "✅ Permissions set"

# Build assets
echo "🏗️  Building frontend assets..."
npm run dev
echo "✅ Assets built"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Configure your database settings in .env"
echo "2. Run: php artisan migrate"
echo "3. Configure mail settings in .env (optional)"
echo "4. Configure reCAPTCHA keys in .env (optional)"
echo "5. Start the development server: php artisan serve"
echo ""
echo "📚 For detailed configuration help, see: docs/environment-setup.md"
echo ""
