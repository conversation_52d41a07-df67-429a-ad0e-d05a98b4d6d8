<?php
/**
 * <PERSON><PERSON> Deployment Diagnostic Script
 * Upload this file to your public directory and visit it in your browser
 * DELETE THIS FILE AFTER USE FOR SECURITY!
 */

// Prevent caching
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

echo "<!DOCTYPE html>
<html>
<head>
    <title>Laravel Deployment Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .delete-warning { background: #ffebee; padding: 15px; border: 2px solid red; margin: 20px 0; }
    </style>
</head>
<body>";

echo "<h1>🔍 Laravel Deployment Diagnostic</h1>";
echo "<p><strong>Site:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Function to display status
function displayStatus($condition, $message) {
    $class = $condition ? 'success' : 'error';
    $icon = $condition ? '✅' : '❌';
    echo "<div class='$class'>$icon $message</div>";
    return $condition;
}

function displayWarning($message) {
    echo "<div class='warning'>⚠️ $message</div>";
}

function displayInfo($message) {
    echo "<div class='info'>ℹ️ $message</div>";
}

// Check 1: PHP Version
echo "<div class='section'>";
echo "<h2>1. PHP Environment</h2>";
$phpVersion = phpversion();
echo "<strong>PHP Version:</strong> $phpVersion<br>";
displayStatus(version_compare($phpVersion, '8.2.0', '>='), "PHP 8.2+ required for Laravel 11");

// Check required extensions
$requiredExtensions = ['openssl', 'pdo', 'mbstring', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath'];
echo "<br><strong>Required PHP Extensions:</strong><br>";
foreach ($requiredExtensions as $ext) {
    displayStatus(extension_loaded($ext), "Extension: $ext");
}
echo "</div>";

// Check 2: File Structure
echo "<div class='section'>";
echo "<h2>2. File Structure</h2>";
$laravelRoot = dirname(__FILE__);
$isInPublic = basename($laravelRoot) === 'public';

if ($isInPublic) {
    $laravelRoot = dirname($laravelRoot);
    displayStatus(true, "Script is in public directory");
} else {
    displayWarning("Script should be in public directory for accurate testing");
}

echo "<br><strong>Laravel Root:</strong> $laravelRoot<br>";

// Check essential files
$essentialFiles = [
    'vendor/autoload.php' => 'Composer autoloader',
    'bootstrap/app.php' => 'Laravel bootstrap',
    '.env' => 'Environment file',
    'artisan' => 'Artisan command file'
];

foreach ($essentialFiles as $file => $description) {
    $exists = file_exists("$laravelRoot/$file");
    displayStatus($exists, "$description ($file)");
}
echo "</div>";

// Check 3: Laravel Bootstrap
echo "<div class='section'>";
echo "<h2>3. Laravel Bootstrap</h2>";
try {
    if (file_exists("$laravelRoot/vendor/autoload.php")) {
        require_once "$laravelRoot/vendor/autoload.php";
        displayStatus(true, "Composer autoloader loaded");
        
        if (file_exists("$laravelRoot/bootstrap/app.php")) {
            $app = require_once "$laravelRoot/bootstrap/app.php";
            displayStatus(true, "Laravel application bootstrapped");
            
            // Try to get Laravel version
            if (class_exists('Illuminate\Foundation\Application')) {
                $version = $app->version();
                echo "<div class='info'>Laravel Version: $version</div>";
            }
        } else {
            displayStatus(false, "bootstrap/app.php not found");
        }
    } else {
        displayStatus(false, "vendor/autoload.php not found - run 'composer install'");
    }
} catch (Exception $e) {
    displayStatus(false, "Laravel bootstrap failed: " . $e->getMessage());
}
echo "</div>";

// Check 4: Environment Configuration
echo "<div class='section'>";
echo "<h2>4. Environment Configuration</h2>";

if (file_exists("$laravelRoot/.env")) {
    displayStatus(true, ".env file exists");
    
    // Parse .env file
    $envContent = file_get_contents("$laravelRoot/.env");
    $envLines = explode("\n", $envContent);
    $envVars = [];
    
    foreach ($envLines as $line) {
        $line = trim($line);
        if (!empty($line) && strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $envVars[trim($key)] = trim($value, '"\'');
        }
    }
    
    // Check critical env variables
    $criticalVars = ['APP_KEY', 'APP_ENV', 'APP_DEBUG', 'DB_CONNECTION'];
    foreach ($criticalVars as $var) {
        $exists = isset($envVars[$var]) && !empty($envVars[$var]);
        displayStatus($exists, "$var is set");
        if ($exists && $var === 'APP_KEY') {
            $keyLength = strlen($envVars[$var]);
            if ($keyLength < 32) {
                displayWarning("APP_KEY seems too short ($keyLength chars) - run 'php artisan key:generate'");
            }
        }
    }
    
    // Display some env info
    if (isset($envVars['APP_ENV'])) {
        echo "<div class='info'>Environment: " . $envVars['APP_ENV'] . "</div>";
    }
    if (isset($envVars['APP_DEBUG'])) {
        echo "<div class='info'>Debug Mode: " . $envVars['APP_DEBUG'] . "</div>";
    }
    
} else {
    displayStatus(false, ".env file missing - copy from .env.example");
}
echo "</div>";

// Check 5: Storage and Permissions
echo "<div class='section'>";
echo "<h2>5. Storage and Permissions</h2>";

$storageDirectories = [
    'storage/logs' => 'Log directory',
    'storage/framework/cache' => 'Cache directory',
    'storage/framework/sessions' => 'Sessions directory',
    'storage/framework/views' => 'Views cache directory',
    'bootstrap/cache' => 'Bootstrap cache directory'
];

foreach ($storageDirectories as $dir => $description) {
    $fullPath = "$laravelRoot/$dir";
    $exists = is_dir($fullPath);
    $writable = $exists && is_writable($fullPath);
    
    if ($exists) {
        displayStatus($writable, "$description is writable");
        if (!$writable) {
            echo "<div class='info'>Fix with: chmod -R 755 $dir</div>";
        }
    } else {
        displayStatus(false, "$description does not exist");
        echo "<div class='info'>Create with: mkdir -p $dir</div>";
    }
}

// Check storage symlink
$publicStorage = $isInPublic ? 'storage' : 'public/storage';
if (is_link($publicStorage)) {
    displayStatus(true, "Storage symlink exists");
} else {
    displayStatus(false, "Storage symlink missing - run 'php artisan storage:link'");
}
echo "</div>";

// Check 6: Database Connection
echo "<div class='section'>";
echo "<h2>6. Database Connection</h2>";

if (isset($envVars)) {
    $dbVars = ['DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME'];
    $hasDbConfig = true;
    
    foreach ($dbVars as $var) {
        if (!isset($envVars[$var]) || empty($envVars[$var])) {
            $hasDbConfig = false;
            displayStatus(false, "$var is not set");
        }
    }
    
    if ($hasDbConfig) {
        displayStatus(true, "Database configuration variables are set");
        
        // Try database connection
        try {
            $dsn = $envVars['DB_CONNECTION'] . ':host=' . $envVars['DB_HOST'] . ';port=' . ($envVars['DB_PORT'] ?? 3306) . ';dbname=' . $envVars['DB_DATABASE'];
            $pdo = new PDO($dsn, $envVars['DB_USERNAME'], $envVars['DB_PASSWORD'] ?? '');
            displayStatus(true, "Database connection successful");
        } catch (Exception $e) {
            displayStatus(false, "Database connection failed: " . $e->getMessage());
        }
    }
} else {
    displayStatus(false, "Cannot check database - .env file not readable");
}
echo "</div>";

// Check 7: Web Server Configuration
echo "<div class='section'>";
echo "<h2>7. Web Server Configuration</h2>";

echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>Script Path:</strong> " . __FILE__ . "<br>";

// Check if .htaccess exists (for Apache)
if (file_exists('.htaccess')) {
    displayStatus(true, ".htaccess file exists");
} else {
    displayWarning(".htaccess file missing - may cause routing issues");
}

// Check mod_rewrite (Apache)
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    displayStatus(in_array('mod_rewrite', $modules), "Apache mod_rewrite enabled");
}

echo "</div>";

// Check 8: Recent Error Logs
echo "<div class='section'>";
echo "<h2>8. Recent Error Logs</h2>";

$logFile = "$laravelRoot/storage/logs/laravel.log";
if (file_exists($logFile) && is_readable($logFile)) {
    displayStatus(true, "Laravel log file accessible");
    
    // Get last 20 lines of log
    $lines = file($logFile);
    $recentLines = array_slice($lines, -20);
    
    echo "<strong>Last 20 lines of laravel.log:</strong>";
    echo "<pre>" . htmlspecialchars(implode('', $recentLines)) . "</pre>";
} else {
    displayStatus(false, "Laravel log file not accessible");
}
echo "</div>";

// Quick Fix Commands
echo "<div class='section'>";
echo "<h2>9. Quick Fix Commands</h2>";
echo "<p>If you have SSH/terminal access, try these commands:</p>";
echo "<pre>";
echo "# Fix permissions\n";
echo "chmod -R 755 storage bootstrap/cache\n\n";
echo "# Generate app key\n";
echo "php artisan key:generate\n\n";
echo "# Create storage symlink\n";
echo "php artisan storage:link\n\n";
echo "# Clear and rebuild caches\n";
echo "php artisan optimize:clear\n";
echo "php artisan config:cache\n";
echo "php artisan route:cache\n";
echo "php artisan view:cache\n";
echo "</pre>";
echo "</div>";

// Security Warning
echo "<div class='delete-warning'>";
echo "<h2>🚨 SECURITY WARNING</h2>";
echo "<p><strong>DELETE THIS FILE IMMEDIATELY AFTER USE!</strong></p>";
echo "<p>This diagnostic script exposes sensitive information about your server configuration.</p>";
echo "<p>File location: " . __FILE__ . "</p>";
echo "</div>";

echo "</body></html>";

// Optional: Auto-delete after 1 hour (uncomment if desired)
// if (filemtime(__FILE__) < time() - 3600) {
//     unlink(__FILE__);
// }
?>
