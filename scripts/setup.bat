@echo off
echo 🚀 Setting up Neuronic Lab Laravel Application...
echo ==================================================

REM Check if .env exists
if not exist .env (
    echo 📋 Creating .env file from .env.example...
    copy .env.example .env
    echo ✅ .env file created
) else (
    echo ⚠️  .env file already exists, skipping...
)

REM Install Composer dependencies
if not exist vendor (
    echo 📦 Installing Composer dependencies...
    composer install
    echo ✅ Composer dependencies installed
) else (
    echo ⚠️  Vendor directory exists, running composer update...
    composer update
)

REM Install NPM dependencies
if not exist node_modules (
    echo 📦 Installing NPM dependencies...
    npm install
    echo ✅ NPM dependencies installed
) else (
    echo ⚠️  Node modules exist, running npm update...
    npm update
)

REM Generate application key
echo 🔑 Generating application key...
php artisan key:generate
echo ✅ Application key generated

REM Create storage symlink
echo 🔗 Creating storage symlink...
php artisan storage:link
echo ✅ Storage symlink created

REM Build assets
echo 🏗️  Building frontend assets...
npm run dev
echo ✅ Assets built

echo.
echo 🎉 Setup completed successfully!
echo.
echo 📝 Next steps:
echo 1. Configure your database settings in .env
echo 2. Run: php artisan migrate
echo 3. Configure mail settings in .env (optional)
echo 4. Configure reCAPTCHA keys in .env (optional)
echo 5. Start the development server: php artisan serve
echo.
echo 📚 For detailed configuration help, see: docs/environment-setup.md
echo.
pause
