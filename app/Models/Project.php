<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'language_id',
        'project_category_id',
        'photo_id',
        'title',
        'slug',
        'body',
        'sort_order',
        'excerpt',
        'meta_title',
        'meta_description',
        'image_featured2',
        'img_gal1',
        'img_gal2',
        'img_gal3',
        'img_gal4',
        'date',
        'client',
        'button_text',
        'button_link',
        'youtube_video_url',
        'media_type',
    ];

    public function user() {
        return $this->belongsTo('App\Models\User');
    }

    public function project_category() {
        return $this->belongsTo('App\Models\ProjectCategory');
    }

    public function photo() {
        return $this->belongsTo('App\Models\Photo');
    }

    /**
     * Extract YouTube video ID from various YouTube URL formats
     */
    public function getYoutubeVideoId()
    {
        if (!$this->youtube_video_url) {
            return null;
        }

        $pattern = '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/';
        preg_match($pattern, $this->youtube_video_url, $matches);

        return isset($matches[1]) ? $matches[1] : null;
    }

    /**
     * Get YouTube video thumbnail URL with fallback mechanism
     */
    public function getYoutubeThumbnail($quality = 'maxresdefault')
    {
        $videoId = $this->getYoutubeVideoId();
        if (!$videoId) {
            return null;
        }

        return "https://img.youtube.com/vi/{$videoId}/{$quality}.jpg";
    }

    /**
     * Get YouTube video thumbnail URL with automatic fallback
     * Tries different qualities in order: maxresdefault -> hqdefault -> mqdefault -> default
     */
    public function getYoutubeThumbnailWithFallback()
    {
        $videoId = $this->getYoutubeVideoId();
        if (!$videoId) {
            return null;
        }

        // Return a data URL that will be handled by JavaScript for fallback
        return "https://img.youtube.com/vi/{$videoId}/maxresdefault.jpg";
    }

    /**
     * Get YouTube embed URL
     */
    public function getYoutubeEmbedUrl()
    {
        $videoId = $this->getYoutubeVideoId();
        if (!$videoId) {
            return null;
        }

        return "https://www.youtube.com/embed/{$videoId}";
    }

    /**
     * Check if project uses video media type
     */
    public function isVideoType()
    {
        return $this->media_type === 'video';
    }

    /**
     * Get the main media URL (image or video thumbnail)
     */
    public function getMainMediaUrl()
    {
        if ($this->isVideoType()) {
            return $this->getYoutubeThumbnail();
        }

        return $this->photo ? '/images/media/' . $this->photo->file : '/img/200x200.png';
    }
}
