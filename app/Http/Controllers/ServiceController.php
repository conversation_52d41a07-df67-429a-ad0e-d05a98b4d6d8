<?php
namespace App\Http\Controllers;

use App\Models\Service;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Requests\ServiceRequest; 
use App\Models\Photo;
use App\Models\Language;

class ServiceController extends Controller
{
    //


    public function index(Request $request)
    {


        $langs = Language::all();
        $lang = Language::where('code', $request->language)->first();
        $lang_id = $lang->id;

        //return $lang;

        $data['services'] = Service::where('language_id', $lang_id)->orderBy('sort_order', 'ASC')->orderBy('id', 'ASC')->paginate(10000);

        $data['lang_id'] = $lang_id;

        return view('service.service-index', $data, compact('langs'));
    }

    public function create()
    {
        $langs = Language::all();
        return view('service.service-create', compact('langs'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(ServiceRequest $request)
    {


        $input = $request->all();

        if ($file = $request->file('photo_id')) {

            $name = time() . $file->getClientOriginalName();

            $file->move('images/media/', $name);

            $photo = Photo::create(['file'=>$name]);

            $input['photo_id'] = $photo->id;
        }

        // Set sort_order to the next available position
        if (!isset($input['sort_order'])) {
            $maxOrder = Service::where('language_id', $input['language_id'])->max('sort_order');
            $input['sort_order'] = $maxOrder + 1;
        }

        Service::create($input);

        return back()->with('service_success','Service created successfully!');
    }



    public function edit(Service $service)
    {
        return view('service.service-edit', compact('service'));
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\service  $service
     * @return \Illuminate\Http\Response
     */
    public function update(ServiceRequest $request, Service $service)
    {
        
        $input = $request->all();

        if ($file = $request->file('photo_id')) {
            
            $name = time() . $file->getClientOriginalName();

            $file->move('images/media/', $name);

            $photo = Photo::create(['file'=>$name]);

            $input['photo_id'] = $photo->id;
        }


        $service->update($input);

        return back()->with('service_success','Service updated successfully!');
    }

    public function delete_service(Request $request, Service $service) {


        if(isset($request->delete_all) && !empty($request->checkbox_array)) {
            $services = Service::findOrFail($request->checkbox_array);
            foreach ($services as $service) {
                $service->delete();
            }
            return back()->with('services_success','Service/s deleted successfully!');
        } else {
            return back();
        }

        $services = Service::findOrFail($request->checkbox_array);
        foreach ($services as $service) {
            $service->delete();
        }

        return back();
        //return 'works';
    }

    public function reorder(Request $request)
    {
        $request->validate([
            'services' => 'required|array',
            'services.*.id' => 'required|integer|exists:services,id',
            'services.*.sort_order' => 'required|integer|min:0'
        ]);

        try {
            foreach ($request->services as $serviceData) {
                Service::where('id', $serviceData['id'])
                    ->update(['sort_order' => $serviceData['sort_order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Services reordered successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder services: ' . $e->getMessage()
            ], 500);
        }
    }

}
