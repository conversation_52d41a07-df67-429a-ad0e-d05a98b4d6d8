<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\YouTubeUrl;

class ProjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'title'=> 'required',
            'slug'=> 'required|unique:projects,slug',
            'body'=> 'required',
            'meta_title'=> 'required',
            'meta_description'=> 'required',
            'media_type' => 'required|in:image,video',
        ];

        // Conditional validation based on media type
        if ($this->input('media_type') === 'image') {
            $rules['photo_id'] = 'required';
            $rules['youtube_video_url'] = 'nullable';
        } else {
            $rules['youtube_video_url'] = [
                'required',
                'url',
                new YouTubeUrl()
            ];
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'photo_id.required' => 'Please upload an image when image media type is selected.',
            'youtube_video_url.required' => 'Please enter a YouTube URL when video media type is selected.',
            'youtube_video_url.url' => 'Please enter a valid URL format.',
        ];
    }
}
