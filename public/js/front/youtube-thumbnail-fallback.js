/**
 * YouTube Thumbnail Fallback Handler
 * Automatically handles fallback for YouTube thumbnails when higher quality versions are not available
 */

function handleYoutubeThumbnailError(img) {
    const videoId = img.getAttribute('data-video-id');
    if (!videoId) {
        img.src = '/img/200x200.png';
        return;
    }
    
    // Define fallback qualities in order of preference
    const qualities = ['hqdefault', 'mqdefault', 'default'];
    const currentSrc = img.src;
    
    // Find current quality and try next one
    let currentQualityIndex = -1;
    if (currentSrc.includes('maxresdefault')) {
        currentQualityIndex = -1;
    } else if (currentSrc.includes('hqdefault')) {
        currentQualityIndex = 0;
    } else if (currentSrc.includes('mqdefault')) {
        currentQualityIndex = 1;
    } else if (currentSrc.includes('default')) {
        currentQualityIndex = 2;
    }
    
    const nextQualityIndex = currentQualityIndex + 1;
    
    if (nextQualityIndex < qualities.length) {
        const nextQuality = qualities[nextQualityIndex];
        img.src = `https://img.youtube.com/vi/${videoId}/${nextQuality}.jpg`;
    } else {
        // All YouTube qualities failed, use fallback image
        img.src = '/img/200x200.png';
    }
}

/**
 * Handle YouTube thumbnail fallback for background images
 */
function handleYoutubeBackgroundThumbnailError(element) {
    const videoId = element.getAttribute('data-video-id');
    if (!videoId) {
        element.style.backgroundImage = 'url(/img/200x200.png)';
        return;
    }

    // Define fallback qualities in order of preference
    const qualities = ['hqdefault', 'mqdefault', 'default'];
    const currentBg = element.style.backgroundImage;

    // Find current quality and try next one
    let currentQualityIndex = -1;
    if (currentBg.includes('maxresdefault')) {
        currentQualityIndex = -1;
    } else if (currentBg.includes('hqdefault')) {
        currentQualityIndex = 0;
    } else if (currentBg.includes('mqdefault')) {
        currentQualityIndex = 1;
    } else if (currentBg.includes('default')) {
        currentQualityIndex = 2;
    }

    const nextQualityIndex = currentQualityIndex + 1;

    if (nextQualityIndex < qualities.length) {
        const nextQuality = qualities[nextQualityIndex];
        element.style.backgroundImage = `url(https://img.youtube.com/vi/${videoId}/${nextQuality}.jpg)`;
    } else {
        // All YouTube qualities failed, use fallback image
        element.style.backgroundImage = 'url(/img/200x200.png)';
    }
}

/**
 * Test if a background image loads successfully
 */
function testBackgroundImage(url, callback) {
    const img = new Image();
    img.onload = function() {
        callback(true);
    };
    img.onerror = function() {
        callback(false);
    };
    img.src = url;
}

// Initialize YouTube thumbnail fallback handling
document.addEventListener('DOMContentLoaded', function() {
    // Handle regular img elements with youtube-thumbnail class
    const youtubeThumbnails = document.querySelectorAll('.youtube-thumbnail');

    youtubeThumbnails.forEach(function(img) {
        // Handle immediate errors
        img.addEventListener('error', function() {
            handleYoutubeThumbnailError(this);
        });

        // Check if image failed to load by checking naturalWidth
        img.addEventListener('load', function() {
            if (this.naturalWidth === 0) {
                handleYoutubeThumbnailError(this);
            }
        });

        // For lazy loaded images, we need to handle the data-src attribute
        if (img.hasAttribute('data-src')) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
                        // Image src was updated by lazy loading, check if it's a YouTube thumbnail
                        if (img.src.includes('youtube.com')) {
                            img.addEventListener('error', function() {
                                handleYoutubeThumbnailError(this);
                            });
                        }
                    }
                });
            });
            observer.observe(img, { attributes: true });
        }
    });

    // Handle YouTube video containers (new improved structure)
    const youtubeVideoContainers = document.querySelectorAll('.youtube-video-container');

    youtubeVideoContainers.forEach(function(container) {
        const projectUrl = container.getAttribute('data-project-url');
        if (projectUrl) {
            // Add click handler to navigate to project page
            function navigateToProject() {
                window.location.href = projectUrl;
            }

            container.addEventListener('click', navigateToProject);

            // Add keyboard accessibility
            container.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    navigateToProject();
                }
            });
        }
    });

    // Handle background image YouTube thumbnails (legacy support)
    const youtubeBackgroundThumbnails = document.querySelectorAll('.youtube-video-thumbnail');

    youtubeBackgroundThumbnails.forEach(function(element) {
        const videoId = element.getAttribute('data-video-id');
        if (videoId) {
            // Test if the maxresdefault image loads
            const maxresUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
            testBackgroundImage(maxresUrl, function(success) {
                if (!success) {
                    handleYoutubeBackgroundThumbnailError(element);
                }
            });

            // Add click handler to navigate to project page (if project URL is available)
            const projectUrl = element.getAttribute('data-project-url');
            if (projectUrl) {
                element.addEventListener('click', function() {
                    window.location.href = projectUrl;
                });
            } else {
                // Fallback to YouTube if no project URL
                element.addEventListener('click', function() {
                    const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;
                    window.open(youtubeUrl, '_blank');
                });
            }

            // Make it look clickable
            element.style.cursor = 'pointer';
        }
    });
});
