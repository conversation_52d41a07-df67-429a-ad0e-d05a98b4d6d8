/*!
 * simpleParallax.min - simpleParallax is a simple JavaScript library that gives your website parallax animations on any images, 
 * @version: 5.0.2,
 * @link: https://simpleparallax.com/
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("simpleParallax",[],t):"object"==typeof exports?exports.simpleParallax=t():e.simpleParallax=t()}(window,function(){return(n={},s.m=i=[function(e,t,i){"use strict";function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}i.r(t);var s=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.positions={top:0,bottom:0,height:0}}return function(e,t,i){t&&n(e.prototype,t),i&&n(e,i)}(e,[{key:"setViewportTop",value:function(){return this.positions.top=window.pageYOffset,this.positions}},{key:"setViewportBottom",value:function(){return this.positions.bottom=this.positions.top+this.positions.height,this.positions}},{key:"setViewportHeight",value:function(){return this.positions.height=document.documentElement.clientHeight,this.positions}},{key:"setViewportAll",value:function(){return this.positions.top=window.pageYOffset,this.positions.bottom=this.positions.top+this.positions.height,this.positions.height=document.documentElement.clientHeight,this.positions}}]),e}(),o=function(e){return NodeList.prototype.isPrototypeOf(e)?e:HTMLCollection.prototype.isPrototypeOf(e)?Array.from(e):[e]},r=function(){for(var e,t="transform webkitTransform mozTransform oTransform msTransform".split(" "),i=0;void 0===e;)e=void 0!==document.createElement("div").style[t[i]]?t[i]:void 0,i++;return e}(),a=function(e){return!!e&&(!!e.complete&&(void 0===e.naturalWidth||0!==e.naturalWidth))};function l(e){return function(e){if(Array.isArray(e)){for(var t=0,i=new Array(e.length);t<e.length;t++)i[t]=e[t];return i}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function h(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var u=function(){function i(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),this.element=e,this.elementContainer=e,this.settings=t,this.isVisible=!0,this.isInit=!1,this.oldTranslateValue=-1,this.init=this.init.bind(this),a(e)?this.init():this.element.addEventListener("load",this.init)}return function(e,t,i){t&&h(e.prototype,t),i&&h(e,i)}(i,[{key:"init",value:function(){this.isInit||(!1===this.settings.overflow&&this.wrapElement(this.element),this.setStyle(),this.getElementOffset(),this.intersectionObserver(),this.getTranslateValue(),this.animate(),this.isInit=!0)}},{key:"wrapElement",value:function(){var e=this.element.closest("picture")||this.element,t=document.createElement("div");t.classList.add("simpleParallax"),t.style.overflow="hidden",e.parentNode.insertBefore(t,e),t.appendChild(e),this.elementContainer=t}},{key:"unWrapElement",value:function(){var e=this.elementContainer;e.replaceWith.apply(e,l(e.childNodes))}},{key:"setStyle",value:function(){!1===this.settings.overflow&&(this.element.style[r]="scale("+this.settings.scale+")"),0<this.settings.delay&&(this.element.style.transition="transform "+this.settings.delay+"s "+this.settings.transition),this.element.style.willChange="transform"}},{key:"unSetStyle",value:function(){this.element.style.willChange="",this.element.style[r]="",this.element.style.transition=""}},{key:"getElementOffset",value:function(){var e=this.elementContainer.getBoundingClientRect();this.elementHeight=e.height,this.elementTop=e.top+m.positions.top,this.elementBottom=this.elementHeight+this.elementTop}},{key:"buildThresholdList",value:function(){for(var e=[],t=1;t<=this.elementHeight;t++){var i=t/this.elementHeight;e.push(i)}return e}},{key:"intersectionObserver",value:function(){var e={root:null,threshold:this.buildThresholdList()};this.observer=new IntersectionObserver(this.intersectionObserverCallback.bind(this),e),this.observer.observe(this.element)}},{key:"intersectionObserverCallback",value:function(e){for(var t=e.length-1;0<=t;t--)e[t].isIntersecting?this.isVisible=!0:this.isVisible=!1}},{key:"checkIfVisible",value:function(){return this.elementBottom>m.positions.top&&this.elementTop<m.positions.bottom}},{key:"getRangeMax",value:function(){var e=this.element.clientHeight;this.rangeMax=e*this.settings.scale-e}},{key:"getTranslateValue",value:function(){var e=((m.positions.bottom-this.elementTop)/((m.positions.height+this.elementHeight)/100)).toFixed(1);return e=Math.min(100,Math.max(0,e)),this.oldPercentage!==e&&(this.rangeMax||this.getRangeMax(),this.translateValue=(e/100*this.rangeMax-this.rangeMax/2).toFixed(0),this.oldTranslateValue!==this.translateValue&&(this.oldPercentage=e,this.oldTranslateValue=this.translateValue,!0))}},{key:"animate",value:function(){var e,t=0,i=0;(this.settings.orientation.includes("left")||this.settings.orientation.includes("right"))&&(i=(this.settings.orientation.includes("left")?-1*this.translateValue:this.translateValue)+"px"),(this.settings.orientation.includes("up")||this.settings.orientation.includes("down"))&&(t=(this.settings.orientation.includes("up")?-1*this.translateValue:this.translateValue)+"px"),e=!1===this.settings.overflow?"translate3d("+i+", "+t+", 0) scale("+this.settings.scale+")":"translate3d("+i+", "+t+", 0)",this.element.style[r]=e}}]),i}();function c(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}i.d(t,"viewport",function(){return m}),i.d(t,"default",function(){return y});var f,p,m=new s,d=!0,g=!1,v=[],y=function(){function i(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),e&&(this.elements=o(e),this.defaults={delay:.4,orientation:"up",scale:1.3,overflow:!1,transition:"cubic-bezier(0,0,0,1)",breakpoint:!1},this.settings=Object.assign(this.defaults,t),this.settings.breakpoint&&document.documentElement.clientWidth<=this.settings.breakpoint||(!1 in window&&(d=!1),this.lastPosition=-1,this.handleResize=this.handleResize.bind(this),this.proceedRequestAnimationFrame=this.proceedRequestAnimationFrame.bind(this),this.init()))}return function(e,t,i){t&&c(e.prototype,t),i&&c(e,i)}(i,[{key:"init",value:function(){m.setViewportAll();for(var e=this.elements.length-1;0<=e;e--){var t=new u(this.elements[e],this.settings);v.push(t)}f=v.length,g||(this.proceedRequestAnimationFrame(),window.addEventListener("resize",this.handleResize),g=!0)}},{key:"handleResize",value:function(){m.setViewportAll(),this.settings.breakpoint&&document.documentElement.clientWidth<=this.settings.breakpoint&&this.destroy();for(var e=f-1;0<=e;e--)v[e].getElementOffset(),v[e].getRangeMax()}},{key:"proceedRequestAnimationFrame",value:function(){if(m.setViewportTop(),this.lastPosition!==m.positions.top){m.setViewportBottom();for(var e=f-1;0<=e;e--)this.proceedElement(v[e]);p=window.requestAnimationFrame(this.proceedRequestAnimationFrame),this.lastPosition=m.positions.top}else p=window.requestAnimationFrame(this.proceedRequestAnimationFrame)}},{key:"proceedElement",value:function(e){(d?e.isVisible:e.checkIfVisible())&&e.getTranslateValue()&&e.animate()}},{key:"destroy",value:function(){for(var e=[],t=f-1;0<=t;t--)for(var i=this.elements.length-1;0<=i;i--)if(v[t].element===this.elements[i]){e.push(t);break}for(var n=0;n<e.length;n++){var s=e[n];v[s].unSetStyle(),!1===this.settings.overflow&&v[s].unWrapElement(),v=v.slice(0,s).concat(v.slice(s+1,v.length))}(f=v.length)||(window.cancelAnimationFrame(p),window.removeEventListener("resize",this.handleResize))}}]),i}()}],s.c=n,s.d=function(e,t,i){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(t,e){if(1&e&&(t=s(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(s.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)s.d(i,n,function(e){return t[e]}.bind(null,n));return i},s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="",s(s.s=0)).default;function s(e){if(n[e])return n[e].exports;var t=n[e]={i:e,l:!1,exports:{}};return i[e].call(t.exports,t,t.exports,s),t.l=!0,t.exports}var i,n});