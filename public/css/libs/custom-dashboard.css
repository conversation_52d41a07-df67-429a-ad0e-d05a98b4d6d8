/*dashboard users index*/
.float-left-user-name {
    float: left;
}
.float-left-avatar {
    float: left;
    margin-right: 10px;
}
.float-left-user-name p {
    margin: 0 0 2px;
    line-height: 1;
}
.float-left-user-name a {
    font-size: 14px;
    padding-right: 5px;
}
.float-left-avatar img {
    border: 1px solid #e3e6f0;
}
.media-index img {
    max-width: 150px;
    height: auto;
    background: #f7f7f7;
}
select.form-control.language-control {
    max-width: fit-content;
    margin-left: auto;
}
.tox-notification {
    display: none !important;
}
input.delete_single_user {
    background: transparent;
    outline: 0;
    border: 0;
    color: red;
    font-size: 14px;
    padding: 0;
    padding-right: 5px;
}
nav.flex.items-center.justify-between {
    text-align: center;
}
 .ql-editor ol {
    padding-left: 20px;
    list-style-type: decimal;
  }

  .ql-editor ul {
    padding-left: 20px;
    list-style-type: disc;
  }

  .ql-editor li {
    margin-bottom: 5px;
  }
.table-responsive svg {
    max-width: 20px;
}
td.page-title {
    min-width: 230px;
}
td.page-url {
    min-width: 350px;
}
td.pricing-title-dash * {
    font-size: 17px;
    max-width: 400px;
}
td.menu-name {
    min-width: 150px;
}

td.menu-link {
    min-width: 400px;
}
td.body-project {
    max-width: 400px;
}
.table-responsive nav .flex.justify-between.flex-1.sm\:hidden {
    display: none;
}
.bg-gradient-primary {
    background-color: #16151A;
    background-image: linear-gradient(
180deg
, #000 10%, #16151A 100%);
    background-size: cover;
}
table#dataTable thead, table#dataTable tfoot {
    background: #f7f7f7;
}
td.body-post p {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
nav.flex.items-center.justify-between .leading-5 {
    padding: 5px 10px !important;
    margin: 5px 2px !important;
}
nav.flex.items-center.justify-between  .shadow-sm {
    box-shadow: none !important;
}
td.body-post {
    max-width: 400px;
}
.btn.btn-primary {
    background: #6022ea;
    font-weight: 600;
}
.table-responsive input.btn.btn-primary {
    margin-left: 5px;
}
.card-body .alert ul {
    margin: 0;
}
.form-group {
    margin-bottom: 20px;
    background: #f7f7f7;
    padding: 20px;
    border: 1px solid #e3e6f0;
}
table#dataTable tbody tr:nth-child(2n) {
    background: #f7f7f7;
}
a.btn.btn-primary.btn-back {
    margin-bottom: 20px;
}
.img-container img {
    border: 1px solid #ccc;
    border-radius: 5px;
}
a.view-website-link {
    margin: 19px 10px;
}

.table-responsive {
    overflow: unset;
}
.mce-notification {
    display: none;
}
body#page-top form .form-group strong {
    /* display: block; */
    /* margin-bottom: 15px; */
}
.slug-container span {
    display: inline-block;
    margin-right: 20px;
}
.slug-container input {
    display: inline-block;
    width: auto;
}
.hide {
    display: none;
}
@media(max-width: 1024px) {
    .table-responsive {
        overflow-x: auto;
    }
}


  #editor-container {
    height: 300px;
    border: 1px solid #ccc;
  }

  #image-editor-modal {
    display: none;
    position: fixed;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 60%;
    background: #fff;
    border: 1px solid #ccc;
    z-index: 9999;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  #image-src, #image-alt {
    width: 100%;
    margin-bottom: 10px;
    padding: 8px;
    border: 1px solid #ccc;
  }

  #save-image, #cancel-image {
    padding: 10px 20px;
    margin-right: 10px;
    border: none;
    background: #007bff;
    color: white;
    cursor: pointer;
  }

  #cancel-image {
    background: #6c757d;
  }

  #save-image:hover, #cancel-image:hover {
    opacity: 0.9;
  }

    .ql-custom-add-image::before {
    content: '\f030'; /* Font Awesome cod pentru imagine (cameră) */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
  }
 
  .ql-custom-add-link::before {
    content: '\f0c1'; /* Font Awesome cod pentru icon link */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
  }

  /* Stilizare modal link */
  #link-editor-modal {
    display: none;
    position: fixed;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 60%;
    background: #fff;
    border: 1px solid #ccc;
    z-index: 9999;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  #link-url, #link-text {
    width: 100%;
    margin-bottom: 10px;
    padding: 8px;
    border: 1px solid #ccc;
  }


   /* Stilizare pentru butonul link */
  .ql-custom-add-link::before {
    content: '\f0c1'; /* Font Awesome cod pentru icon link */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
  }

  /* Stilizare pentru butonul imagine */
  .ql-custom-add-image::before {
    content: '\f030'; /* Font Awesome cod pentru icon imagine */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
  }

  /* Stilizare modal */
  #image-editor-modal, #link-editor-modal {
    display: none;
    position: fixed;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 60%;
    background: #fff;
    border: 1px solid #ccc;
    z-index: 9999;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  #image-src, #image-alt, #link-url, #link-text {
    width: 100%;
    margin-bottom: 10px;
    padding: 8px;
    border: 1px solid #ccc;
  }