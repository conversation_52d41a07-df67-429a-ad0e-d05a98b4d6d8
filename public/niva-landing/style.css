body {
	color: #666666;
	font-size: 16px;
	font-family: 'Nunito', sans-serif;
	line-height: 1.6;
	background: #fff;
	word-wrap: break-word;
	font-weight: 400;
	overflow-x: hidden;
}

:focus {
	outline: none;
}

a {
	transition: 0.5s;
	color: #000;
	text-decoration: none;
}

a:hover,
a:focus {
	text-decoration: none;
	color: #000;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: 600;
	font-family: 'Nunito', sans-serif;
	line-height: 1.4;
	margin-top: 0;
	color: #000;
}

img {
	max-width: 100%;
	height: auto;
}

.text-center {
	text-align: center;
}

.text-left {
	text-align: left;
}

.text-right {
	text-align: right;
}

strong {
	color: #000;
}

p {
	margin-bottom: 10px;
	margin-top: 0;
}

/*
 * Section
 */
section {
	padding-top: 130px;
	background-color: #efefef;
}

section .section-title {
	font-size: 48px;
	font-weight: 800;
	position: relative;
	margin-bottom: 0;
	padding-bottom: 40px;
}

section .section-header {
	margin-bottom: 100px;
}

section .section-header p {
	font-weight: 400;
}

section .section-header strong {
	font-weight: 600;
}

section .item-title {
	font-size: 16px;
	font-weight: 600;
	text-align: center;
	margin: 0;
	padding: 0;
	display: block;
}

section .item-title a {
	color: #000;
}

section p {
	margin-bottom: 0
}

.un-container {
	padding-left: 100px;
	padding-right: 100px;
}

section.section-homepages,
section.section-features {
	padding-top: 140px;
}

section.section-homepages {
	padding-top: 80px;
	position: relative;
	background-color: #fff;
}

section.section-product {
	padding-bottom: 130px;
}

@media (min-width: 1200px) {
	.un-col-md-5 {
		width: 20%;
		float: left;
	}
}

/*
 * Header
 */

.un-header-minimized {
	position: relative;
	height: 140px
}

.site-header {
	padding: 35px 0;
	color: #999999;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 999;
	background-color: transparent;
	transition: padding 0.5s
}

.site-header.minimized {
	padding-top: 20px;
	padding-bottom: 20px;
	background-color: #fff;
	border-bottom: 1px solid #e1e1e1;
}

.site-header .header-links {
	float: right;
	line-height: 52px;
}

.site-header .header-links a {
	font-weight: 500;
	margin-left: 60px;
	position: relative;
}

.site-header .header-links a:after {
	content: '';
	position: absolute;
	width: 0;
	height: 2px;
	right: 0;
	bottom: -5px;
	background-color: #fff;
	transition: 0.5s;
}

.site-header .header-links a:hover:after {
	width: 100%;
	left: 0;
}

.site-header .header-links a.active:after {
	width: 100%;
}

a.purchase {
    display: inline-block;
    width: auto;
    height: 50px;
    font-weight: 800 !important;
    line-height: 50px;
    text-align: center;
    text-transform: capitalize;
    background-color: #6022ea;
    color: #fff;
    font-size: 16px;
    border-radius: 5px;
    padding: 0px 35px;
    border-radius: 100px;
    margin-right: 20px;
}

a.purchase:after {
	display: none;
}

.site-header .header-row {
	display: flex;
	align-items: center;
	justify-content: space-between;

}

.site-header .main-nav {
	display: flex;
	align-items: center;
	position: relative !important;
	justify-content: flex-end;
}

.main-nav ul {
	margin: 0;
	padding: 0;
	list-style: none;
}

.main-nav li {
	display: inline-block;
	margin: 0 20px;
}

.main-nav li:first-child {
	padding-left: 0;
}

.main-nav li:last-child {
	padding-right: 0;
}

.main-nav a {
    font-weight: 800;
    display: block;
    position: relative;
    color: #324452;
    transition: 0.5s;
    font-size: 16px;
}

.main-nav a.purchase {
	margin-left: 30px;
	color: #fff;
}

.main-nav a.purchase:hover {
	background: #324452;
	color: #fff;
}

.col-md-12.footer-extra a:hover {
	background: #324452;
	color: #fff;	
}

.main-nav a:hover,
.main-nav a.active {
	cursor: pointer;
	color: #6022ea;
}

.primary-color {
	color: #6022ea;
}
.logo.col-md-2.col-sm-3.col-xs-12 img {
    max-width: 139px;
}
.site-banner {
    background-image: url(images/slide.jpg);
    position: relative;
    padding-top: 190px;
    padding-bottom: 200px;
    background-position: center;
    background-attachment: fixed;
    background-size: cover;
}

.site-banner .intro-title {
	font-size: 60px;
    font-weight: 800;
    white-space: pre-line;
    line-height: 1.1;
    letter-spacing: -1px;
    margin-bottom: 20px;
}

.site-banner .intro-title .primary-color {
font-weight: 800;
    color: #6022ea;
    -webkit-text-fill-color: #fff;
    -webkit-text-stroke-width: 2px;
    -webkit-text-stroke-color: #6022ea;
    margin-top: 10px;
}
.site-banner .intro-desc {
	margin: 0;
	font-size: 16px;
	white-space: pre-line;
}

.site-banner .intro-desc strong {
	font-weight: 800;
}

/*
 * HomePage Section
 */

.section-homepages {
	background-color: #fff;
}

.section-homepages h3 {
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 40px;
	margin-top: 0;
	text-align: center;
}

.section-homepages h3 a {
	border-bottom: 1px solid transparent;
	transition: 0.5s;
}

.section-homepages h3 a:hover {
	border-color: #000;
}

.homepage-item {
	position: relative;
}

.homepage-item > a {
	display: block;
}

.section .img-item {
	margin-bottom: 25px;
	display: block;
	margin: 0 auto;
}
.list-homepages .img-item {
	 border: 1px solid rgb(204 204 204 / 30%);
} 
   

.section-product .img-item {
	-webkit-box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
	-moz-box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
	box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
}

.section .item {
	margin-bottom: 40px;
}

.section .homepage-item {
	margin-bottom: 40px;
}

.section .homepage-item .item {
	margin-bottom: 0;
	position: relative;
	-webkit-transition: 0.7s;
	-moz-transition: 0.7s;
	transition: 0.7s;
}


.section .homepage-item .item:hover {
	-webkit-transform: translateY(-10px);
	-moz-transform: translateY(-10px);
	transform: translateY(-10px);
	-webkit-box-shadow: 0px 6px 14px 0px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0px 6px 14px 0px rgba(0, 0, 0, 0.2);
	box-shadow: 0px 6px 14px 0px rgba(0, 0, 0, 0.2);
}

.section .homepage-item .item:hover .img-item {
	transform: none;
	box-shadow: none;
}

.section .homepage-item .item h2 {
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: #6022ea;
    font-size: 16px;
    font-weight: 800;
    margin: 0;
    height: 32px;
    padding: 0 13px;
    line-height: 32px;
    color: #fff;
}

.section .item .img-item {
	-webkit-transition: 0.7s;
	-moz-transition: 0.7s;
	transition: 0.7s;
}

.section .item:hover .img-item {
	-webkit-transform: translateY(-10px);
	-moz-transform: translateY(-10px);
	transform: translateY(-10px);
	-webkit-box-shadow: 0px 6px 14px 0px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0px 6px 14px 0px rgba(0, 0, 0, 0.2);
	box-shadow: 0px 6px 14px 0px rgba(0, 0, 0, 0.2);
}

.section-features {
	background-color: #fff;
	padding-bottom: 175px;
}

.section-features .features-item a {
	display: block;
	margin-bottom: 30px;
}

.site-footer {
    padding: 190px 0 180px;
    background: #f7f7f7;
    margin-top: 50px;
}
.site-footer h2 {
	font-size: 48px;
	padding-bottom: 0;
	margin: 0;
	font-weight: 800;
	line-height: 1.2;
	white-space: pre-line;
}

.site-footer p {
	padding-top: 50px;
	padding-bottom: 80px;
	font-size: 20px;
	font-weight: 400;
}

.site-footer a.purchase {
	width: 330px;
	height: 73px;
	font-size: 24px;
	line-height: 75px;
}

/* Slider */
.slick-slider {
	position: relative;
	display: block;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-ms-touch-action: pan-y;
	touch-action: pan-y;
	-webkit-tap-highlight-color: transparent;
}

.slick-list {
	position: relative;
	overflow: hidden;
	display: block;
	margin: 0;
	padding: 0;
}

.slick-list:focus {
	outline: none;
}

.slick-list.dragging {
	cursor: pointer;
	cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
	-webkit-transform: translate3d(0, 0, 0);
	-ms-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}

.slick-track {
	position: relative;
	left: 0;
	top: 0;
	display: block;
}

.slick-track:before,
.slick-track:after {
	content: "";
	display: table;
}

.slick-track:after {
	clear: both;
}

.slick-loading .slick-track {
	visibility: hidden;
}

.slick-slide {
	float: left;
	height: 100%;
	min-height: 1px;
	display: none;
}

[dir="rtl"] .slick-slide {
	float: right;
}

.slick-slide img {
	display: block;
}

.slick-slide.slick-loading img {
	display: none;
}

.slick-slide.dragging img {
	pointer-events: none;
}

.slick-initialized .slick-slide {
	display: block;
}

.slick-loading .slick-slide {
	visibility: hidden;
}

.slick-vertical .slick-slide {
	display: block;
	height: auto;
	border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
	display: none;
}

.list-features {
	margin-bottom: 80px;
}

.list-features .feature-item {
	padding: 0 55px;
	white-space: pre-line;
}

.list-features .feature-item h3 {
	font-size: 24px;
	text-align: left;
	font-weight: 800;
	line-height: 1.2;
	margin-bottom: 0;
}

@media (max-width: 991px) {
	.site-header {
		padding-top: 20px;
		padding-bottom: 20px;
	}

	.site-header .btn-purchase {
		text-align: center;
		margin-top: 10px;
	}
}

@media (max-width: 991px) {
	.site-header .logo {
		text-align: center;
	}

	.site-banner .intro h1 {
		font-size: 24px;
		letter-spacing: 20px;
	}

	.un-container {
		padding-left: 15px;
		padding-right: 15px;
	}

	.site-footer h2 {
		font-size: 24px;
		white-space: normal;
	}

	.main-nav li {
		margin: 0 6px;
	}
}

.sticky-button {
	text-align: center;
	position: fixed;
	bottom: 20px;
	right: 20px;
	background-color: #0072bc;
	color: #fff;
	text-transform: uppercase;
	font-size: 12px;
	padding: 10px 20px 8px;
	border-radius: 5px;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	font-weight: 600;
}

.sticky-button .tf-text {
	background-image: url(images/logoen.png);
	width: 66px;
	height: 12px;
	display: inline-block;
	background-repeat: no-repeat;
}

.sticky-button:hover {
	color: #fff;
}

.sticky-button strong {
	font-size: 20px;
	font-weight: 600;
	line-height: 1.2;
	color: #fff;
	position: relative;
	padding-left: 7px;
}

.section-mobile-version .mobile-item .box-img-item {
	position: relative;
	margin-bottom: 0;
	display: flex;
}


.section-mobile-version .mobile-item .qr-code-img {
	position: relative;
	z-index: 10;
}

.section-mobile-version .mobile-item .qr-code {
	max-width: 155px;
	font-size: 14px;
	color: #000;
	text-align: left;
	white-space: pre-line;
	margin-left: -40px;
	margin-top: 50px;
	position: relative;
}

.section-mobile-version .mobile-item .qr-code-img:after {
	position: absolute;
	bottom: -100%;
	right: 68px;
	background-image: url(images/line.png);
	background-repeat: no-repeat;
	content: "";
	width: 210px;
	height: 197px;
	background-size: cover;
	z-index: -1;
}

.section-mobile-version .mobile-item .qr-code img {
	border: 1px solid #cecece;
	-webkit-box-shadow: 0px 10px 60px 0px rgba(0,0,0,0.15);
	-moz-box-shadow: 0px 10px 60px 0px rgba(0,0,0,0.15);
	box-shadow: 0px 10px 60px 0px rgba(0,0,0,0.15);
}
section#section-mobile-version .section-header.col-md-12.text-center {
    margin-bottom: 30px;
}
.section-mobile-version .mobile-item h2 {
	text-transform: uppercase;
	font-size: 24px;
	margin-top: 50px;
	font-weight: 800;
	text-align: center;
}

.section-mobile-version .list-mobile-version {
	display: flex;
	align-items: center;
	width: 100%;
	padding-left: 40px;
	padding-right: 70px;

}

.section-mobile-version .list-mobile-version .mobile-item {
	width: 33.33%;
	position: relative;
}

.section-mobile-version .mobile-item .box-img {
	position: relative;
	margin-bottom: 0;
	padding-left: 70px;
	padding-right: 70px;
	text-align: center;
}
.section-mobile-version .mobile-item .box-img img {
    max-height: 600px;
    width: auto;
    margin: 0 auto;
}

.section-mobile-version .mobile-item .box-img:before {
	position: absolute;
	bottom: -27px;
	left: 50%;
	transform: translateX(-50%);
	right: 0;
	background-image: url(images/bg_overlay.png);
	background-repeat: no-repeat;
	content: "";
	width: 670px;
	height: 83px;
	background-size: cover;
}

.section-mobile-version {
	background-color: transparent;
	padding-top: 110px;
}

.section-mobile-version .section-title {
	display: inline-block;
	position: relative;
}

.section-mobile-version .section-title small  {
	font-size: 16px;
	position: absolute;
	top: 5px;
	right: -60px;
	background-color: #e42100;
	width: 50px;
	height: 27px;
	line-height: 27px;
	text-align: center;
	color: #fff;
	border-radius: 3px;
	font-weight: 600;
}

.section-mobile-version .mobile-buttons {
	text-align: center;
	margin-top: 20px;
}

.section-mobile-version .mobile-buttons .purchase {
	width: auto;
	padding-left: 20px;
	padding-right: 20px;
}