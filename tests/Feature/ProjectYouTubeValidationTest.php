<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Project;
use App\Models\Language;
use App\Models\ProjectCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProjectYouTubeValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $project;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user with admin privileges
        $this->user = User::factory()->create([
            'role' => 'admin'
        ]);
        
        // Create required dependencies
        $language = Language::factory()->create(['name' => 'English', 'code' => 'en']);
        $category = ProjectCategory::factory()->create(['name' => 'Test Category']);
        
        // Create test project
        $this->project = Project::factory()->create([
            'user_id' => $this->user->id,
            'language_id' => $language->id,
            'project_category_id' => $category->id,
            'media_type' => 'image'
        ]);
    }

    /** @test */
    public function it_validates_youtube_url_when_media_type_is_video()
    {
        $this->actingAs($this->user);

        // Test with valid YouTube URLs
        $validUrls = [
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'https://youtu.be/dQw4w9WgXcQ',
            'https://youtube.com/watch?v=dQw4w9WgXcQ',
            'http://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'https://m.youtube.com/watch?v=dQw4w9WgXcQ'
        ];

        foreach ($validUrls as $url) {
            $response = $this->put(route('project.update', $this->project->id), [
                'title' => 'Test Project',
                'slug' => 'test-project',
                'body' => 'Test content',
                'meta_title' => 'Test Meta Title',
                'meta_description' => 'Test Meta Description',
                'media_type' => 'video',
                'youtube_video_url' => $url,
                'language' => 'en'
            ]);

            $response->assertRedirect();
            $this->assertDatabaseHas('projects', [
                'id' => $this->project->id,
                'media_type' => 'video',
                'youtube_video_url' => $url
            ]);
        }
    }

    /** @test */
    public function it_rejects_invalid_youtube_urls()
    {
        $this->actingAs($this->user);

        $invalidUrls = [
            'https://vimeo.com/123456',
            'https://example.com/video',
            'invalid-url',
            'https://youtube.com/invalid'
        ];

        foreach ($invalidUrls as $url) {
            $response = $this->put(route('project.update', $this->project->id), [
                'title' => 'Test Project',
                'slug' => 'test-project',
                'body' => 'Test content',
                'meta_title' => 'Test Meta Title',
                'meta_description' => 'Test Meta Description',
                'media_type' => 'video',
                'youtube_video_url' => $url,
                'language' => 'en'
            ]);

            $response->assertSessionHasErrors('youtube_video_url');
        }
    }

    /** @test */
    public function it_requires_youtube_url_when_media_type_is_video()
    {
        $this->actingAs($this->user);

        $response = $this->put(route('project.update', $this->project->id), [
            'title' => 'Test Project',
            'slug' => 'test-project',
            'body' => 'Test content',
            'meta_title' => 'Test Meta Title',
            'meta_description' => 'Test Meta Description',
            'media_type' => 'video',
            'youtube_video_url' => '',
            'language' => 'en'
        ]);

        $response->assertSessionHasErrors('youtube_video_url');
    }

    /** @test */
    public function it_allows_nullable_youtube_url_when_media_type_is_image()
    {
        $this->actingAs($this->user);

        $response = $this->put(route('project.update', $this->project->id), [
            'title' => 'Test Project',
            'slug' => 'test-project',
            'body' => 'Test content',
            'meta_title' => 'Test Meta Title',
            'meta_description' => 'Test Meta Description',
            'media_type' => 'image',
            'youtube_video_url' => '',
            'language' => 'en'
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('projects', [
            'id' => $this->project->id,
            'media_type' => 'image',
            'youtube_video_url' => null
        ]);
    }
}
