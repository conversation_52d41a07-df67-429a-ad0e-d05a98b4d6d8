# Neuronic Lab

A modern Laravel-based portfolio and agency website showcasing creative projects and services.

## Features

- **Portfolio Management** - Dynamic project showcase with gallery support
- **Multi-language Support** - Built-in internationalization
- **Blog System** - Content management with rich text editing
- **Contact Forms** - reCAPTCHA protected contact functionality
- **File Management** - Integrated file upload and management system
- **Responsive Design** - Mobile-first responsive layout
- **SEO Optimized** - Meta tags, sitemaps, and search engine optimization

## Tech Stack

- **Backend**: Laravel 11+ with PHP 8.2+
- **Frontend**: Blade templates with Bootstrap 4
- **Database**: MySQL 8.0+
- **Build Tools**: Laravel Mix with Webpack
- **File Management**: UniSharp Laravel File Manager
- **Image Processing**: Intervention Image
- **Security**: reCAPTCHA integration

## Quick Start

### Automated Setup (Recommended)

**For Unix/Linux/macOS:**
```bash
./scripts/setup.sh
```

**For Windows:**
```batch
scripts\setup.bat
```

### Manual Setup

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd neuroniclab.com
   composer install
   npm install
   ```

2. **Environment configuration:**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. **Database setup:**
   ```bash
   # Configure database settings in .env
   php artisan migrate
   ```

4. **Build assets and start:**
   ```bash
   npm run dev
   php artisan serve
   ```

## Configuration

### Environment Variables
See [Environment Setup Guide](docs/environment-setup.md) for detailed configuration instructions.

### Required Services
- **Database**: MySQL 8.0+
- **Mail**: SMTP server for contact forms
- **reCAPTCHA**: Google reCAPTCHA v2 keys

## Development

### Project Structure
```
├── app/                    # Application logic
├── config/                 # Configuration files
├── database/              # Migrations and seeders
├── docs/                  # Project documentation
├── public/                # Web root and assets
├── resources/             # Views, assets, and language files
├── routes/                # Route definitions
├── scripts/               # Setup and utility scripts
├── storage/               # File storage and logs
└── tests/                 # Test files
```

### Key Commands
```bash
# Development server
php artisan serve

# Build assets
npm run dev          # Development build
npm run watch        # Watch for changes
npm run prod         # Production build

# Database
php artisan migrate          # Run migrations
php artisan db:seed         # Seed database

# Cache management
php artisan cache:clear     # Clear application cache
php artisan config:clear    # Clear config cache
php artisan view:clear      # Clear view cache
```

## Documentation

- **[Environment Setup](docs/environment-setup.md)** - Complete environment configuration guide
- **[.gitignore Guide](docs/gitignore-guide.md)** - Git ignore patterns explanation
- **[Storage Migration](docs/storage-migration-implementation.md)** - Storage system migration guide

## Security

### Environment Security
- Never commit `.env` files to version control
- Use strong database passwords
- Enable HTTPS in production
- Configure proper file permissions

### reCAPTCHA
The application uses Google reCAPTCHA v2 for form protection. Configure your keys in the `.env` file:
```env
NOCAPTCHA_SECRET=your_recaptcha_secret_key
NOCAPTCHA_SITEKEY=your_recaptcha_site_key
```

## Deployment

### Production Checklist
- [ ] Set `APP_ENV=production`
- [ ] Set `APP_DEBUG=false`
- [ ] Configure proper database credentials
- [ ] Set up HTTPS
- [ ] Configure mail settings
- [ ] Set up proper file permissions
- [ ] Configure caching (Redis recommended)
- [ ] Set up queue workers if needed

### Build for Production
```bash
npm run prod
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Support

For technical documentation and setup guides, see the `docs/` directory.

## License

This project is proprietary software. All rights reserved.
