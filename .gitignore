# Dependencies
/vendor/
/node_modules/
npm-debug.log
yarn-error.log
package-lock.json

# Laravel Framework
/public/storage
/storage/*.key
/storage/logs/*.log
/storage/framework/cache/data/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/framework/testing/*
bootstrap/cache/*

# Environment & Configuration
.env
.env.backup
.env.production
.env.local
.env.testing
.phpactor.json
auth.json

# IDE & Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Testing & Coverage
.phpunit.result.cache
/coverage/
phpunit.xml.local

# Build & Compilation
/public/build/
/public/mix-manifest.json
/public/css/app.css
/public/js/app.js
/storage/pail

# Laravel Mix Hot Reload
/public/hot

# Development Tools
docker-compose.override.yml
Homestead.json
Homestead.yaml
/.vagrant
.sail/

# Logs & Temporary Files
*.log
/storage/logs/*
!/storage/logs/.gitignore

# Cache & Compiled Files
/cache/
/bootstrap/cache/*
!/bootstrap/cache/.gitignore

# User Uploads (if you want to exclude them)
# /public/images/media/*
# !/public/images/media/.gitignore

# Composer
composer.phar
# Note: composer.lock should be committed in applications, ignored in packages
# composer.lock

# Application Specific
php.ini
server.php

# System Files
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
