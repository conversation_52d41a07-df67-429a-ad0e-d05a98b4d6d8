feat: enhance YouTube thumbnails with industry best practices and fix parallax

- Convert background images to proper <img> elements for SEO and parallax support
- Add comprehensive accessibility features (ARIA labels, keyboard navigation, focus states)
- Implement smooth hover effects and visual feedback
- Enhance error handling with progressive quality fallback
- Add lazy loading for improved performance
- Fix parallax effect on video thumbnails by using thumparallax-down class
- Improve semantic HTML structure with proper button roles
- Add detailed documentation for best practices compliance
- Maintained backward compatibility with existing image-based projects
- Added comprehensive error handling and URL validation for YouTube links
- Changed header__actions__venor to header__actions__darkmoon
- Changed header__menu__venor to header__menu__darkmoon
- Updated venor-animate-border to darkmoon-animate-border
- Modified slider-venor-section to slider-darkmoon-section
- Updated venor-team to darkmoon-team classes
- Changed venor-price-box to darkmoon-price-box

Translation Namespace Fix:
- Renamed language files from niva-backend.php to meridian-backend.php
- Updated all template files to use meridian-backend translations
- Fixed translation keys in all view files
- Cleared application cache to refresh translations

Image Path Fix:
- Fixed broken image loading in admin edit forms
- Corrected image paths from /public/images/media/ to /images/media/
- Fixed image paths from /public/img/ to /img/
- Updated all affected templates for proper image display

Language Files Renamed:
- resources/lang/en/niva-backend.php → meridian-backend.php
- resources/lang/pt/niva-backend.php → meridian-backend.php
- resources/lang/عربى/niva-backend.php → meridian-backend.php

Documentation Updates:
- Updated public/docs/index.html references
- Renamed venor-logo.svg to darkmoon-logo.svg
- Updated installation documentation
- Created comprehensive development backlog in docs/development-backlog.md

Files Modified:
- public/css/front/darkmoon.css (renamed from venor.css)
- public/js/front/darkmoon.js (renamed from venor.js)
- resources/views/layouts/front.blade.php
- resources/views/home.blade.php
- resources/views/project.blade.php
- resources/views/pricing.blade.php
- resources/views/about.blade.php
- resources/views/search.blade.php
- resources/views/page/page-edit.blade.php
- resources/views/project/project-edit.blade.php
- resources/views/post/post-edit.blade.php
- resources/views/language/language-edit.blade.php
- resources/views/slider/slider-edit.blade.php
- resources/views/article.blade.php
- resources/views/settings/contact/contact-edit.blade.php
- resources/views/settings/portfolio/portfolio-edit.blade.php
- public/docs/index.html

Social Media Links Reordered:
- Updated social media links order to: Github, Linkedin, YouTube, Facebook, Instagram, Twitter, Behance
- Added missing GitHub and YouTube social media icons
- Updated all language entries in HeaderFooterSetting table
- Maintained existing CSS styling and hover effects

Application Name Updated:
- Replaced all "Quin CMS" references with "Neuronic Lab" throughout database
- Updated Settings table titles for all languages
- Updated all domain references from quin4.lucian.host to neuroniclab.com
- Updated HeaderFooterSetting, Menu, Slider, and AboutSetting tables
- Updated HomeSetting meta_title for all languages (fixes browser tab title)
- Updated BlogSetting, PortfolioSetting, ContactSetting, PricingSetting meta_title
- Fixed CSS class name from "chat__trigger-quin" to "chat__trigger-neuronic"
- Maintained .env APP_NAME as "Neuronic Lab"
- Admin interface now displays "Neuronic Lab v2.0"
- Browser tab now shows "Neuronic Lab | Creative Agency"
- public/docs/assets/images/darkmoon-logo.svg (renamed)
- docs/development-backlog.md (created)
- All language files in resources/lang/*/meridian-backend.php
