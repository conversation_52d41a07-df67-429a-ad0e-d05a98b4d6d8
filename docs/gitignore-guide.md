# .gitignore Guide

This document explains the `.gitignore` file structure for the Neuronic Lab Laravel application.

## File Structure Overview

The `.gitignore` file is organized into logical sections to make it easy to understand and maintain:

### 1. Dependencies
- **`/vendor/`** - Composer dependencies (should never be committed)
- **`/node_modules/`** - NPM dependencies (should never be committed)
- **`npm-debug.log`** - NPM debug logs
- **`yarn-error.log`** - Yarn error logs
- **`package-lock.json`** - NPM lock file (can be committed or ignored based on team preference)

### 2. Laravel Framework
- **`/public/storage`** - Symbolic link to storage (created by `php artisan storage:link`)
- **`/storage/*.key`** - <PERSON>vel encryption keys
- **`/storage/logs/*.log`** - Application log files
- **`/storage/framework/*`** - Laravel framework cache and compiled files
- **`bootstrap/cache/*`** - Bootstrap cache files

### 3. Environment & Configuration
- **`.env*`** - Environment configuration files (contain sensitive data)
- **`.phpactor.json`** - PHP language server configuration
- **`auth.json`** - Composer authentication file

### 4. IDE & Editor Files
- **`.vscode/`** - Visual Studio Code settings
- **`.idea/`** - PhpStorm/IntelliJ settings
- **`*.swp`, `*.swo`** - Vim swap files
- **`.DS_Store`** - macOS system files
- **`Thumbs.db`** - Windows thumbnail cache

### 5. Testing & Coverage
- **`.phpunit.result.cache`** - PHPUnit result cache
- **`/coverage/`** - Code coverage reports
- **`phpunit.xml.local`** - Local PHPUnit configuration

### 6. Build & Compilation
- **`/public/build/`** - Vite build output
- **`/public/mix-manifest.json`** - Laravel Mix manifest
- **`/public/css/app.css`** - Compiled CSS
- **`/public/js/app.js`** - Compiled JavaScript
- **`/public/hot`** - Laravel Mix hot reload file

### 7. Development Tools
- **`docker-compose.override.yml`** - Local Docker overrides
- **`Homestead.*`** - Laravel Homestead configuration
- **`.vagrant`** - Vagrant configuration
- **`.sail/`** - Laravel Sail configuration

### 8. User Uploads (Optional)
The user uploads section is commented out by default:
```
# /public/images/media/*
# !/public/images/media/.gitignore
```

**Uncomment these lines if you want to exclude user-uploaded images from version control.**

### 9. Application Specific
- **`php.ini`** - Local PHP configuration
- **`server.php`** - Development server script

## Important Notes

### Composer Lock File
The `composer.lock` file is commented out because:
- **For applications**: Should be committed to ensure consistent dependencies
- **For packages**: Should be ignored to allow flexible dependency resolution

### User Uploads
User-uploaded files in `/public/images/media/` are currently tracked. Consider:
- **Exclude them** if they're large or frequently changing
- **Keep them** if they're part of the application's core content

### Environment Files
Never commit `.env` files as they contain:
- Database credentials
- API keys
- Application secrets
- Environment-specific configuration

## Maintenance

When adding new features or tools:
1. Add appropriate ignore patterns to the relevant section
2. Update this documentation
3. Consider the impact on team members and deployment

## Common Patterns to Add

If you add new tools or features, consider these patterns:
```
# For Redis
dump.rdb

# For SQLite
*.sqlite
*.sqlite-journal

# For Telescope
/storage/telescope/

# For Horizon
/storage/horizon/
```
