# Environment Setup Guide

This guide explains how to configure your environment variables for the Neuronic Lab Laravel application.

## Quick Setup

### Automated Setup (Recommended)
```bash
# For Unix/Linux/macOS
./scripts/setup.sh

# For Windows
scripts\setup.bat
```

### Manual Setup

1. **Copy the example file:**
   ```bash
   cp .env.example .env
   ```

2. **Generate application key:**
   ```bash
   php artisan key:generate
   ```

3. **Configure your database and other settings** (see detailed sections below)

## Configuration Sections

### 1. Application Configuration

```env
APP_NAME="Your App Name"          # Display name for your application
APP_ENV=local                     # Environment: local, staging, production
APP_KEY=                          # Auto-generated encryption key
APP_DEBUG=true                    # Enable debug mode (false in production)
APP_URL=http://localhost:8000     # Your application URL
```

**Important Notes:**
- Set `APP_DEBUG=false` in production
- `APP_KEY` is auto-generated with `php artisan key:generate`
- Update `APP_URL` to match your domain in production

### 2. Database Configuration

```env
DB_CONNECTION=mysql               # Database driver
DB_HOST=127.0.0.1                # Database host
DB_PORT=3306                     # Database port
DB_DATABASE=your_database_name   # Database name
DB_USERNAME=your_database_user   # Database username
DB_PASSWORD=your_database_password # Database password
```

**Setup Steps:**
1. Create a MySQL database
2. Update the database credentials
3. Run migrations: `php artisan migrate`

### 3. Mail Configuration

```env
MAIL_MAILER=smtp                 # Mail driver
MAIL_HOST=your_mail_host         # SMTP server
MAIL_PORT=587                    # SMTP port (587 for TLS, 465 for SSL)
MAIL_USERNAME=your_mail_username # SMTP username
MAIL_PASSWORD=your_mail_password # SMTP password
MAIL_ENCRYPTION=tls              # Encryption: tls, ssl, or null
MAIL_FROM_ADDRESS=<EMAIL> # Default sender email
MAIL_FROM_NAME="${APP_NAME}"     # Default sender name
```

**Common SMTP Providers:**
- **Gmail**: `smtp.gmail.com:587` (TLS)
- **Outlook**: `smtp-mail.outlook.com:587` (TLS)
- **SendGrid**: `smtp.sendgrid.net:587` (TLS)
- **Mailgun**: `smtp.mailgun.org:587` (TLS)

### 4. Google reCAPTCHA Configuration

```env
NOCAPTCHA_SECRET=your_recaptcha_secret_key
NOCAPTCHA_SITEKEY=your_recaptcha_site_key
```

**Setup Steps:**
1. Visit [Google reCAPTCHA Admin](https://www.google.com/recaptcha/admin)
2. Create a new site
3. Choose reCAPTCHA v2 "I'm not a robot" checkbox
4. Add your domain(s)
5. Copy the Site Key and Secret Key to your `.env`

### 5. Cache & Session Configuration

```env
CACHE_DRIVER=file                # Cache driver: file, redis, memcached
QUEUE_CONNECTION=sync            # Queue driver: sync, database, redis
SESSION_DRIVER=database          # Session driver: file, database, redis
SESSION_LIFETIME=120             # Session lifetime in minutes
```

**Recommended for Production:**
- Use `redis` for cache and sessions for better performance
- Use `database` or `redis` for queues

### 6. Optional Services

#### AWS S3 (for file storage)
```env
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your_bucket_name
```

#### Pusher (for real-time features)
```env
PUSHER_APP_ID=your_app_id
PUSHER_APP_KEY=your_app_key
PUSHER_APP_SECRET=your_app_secret
PUSHER_APP_CLUSTER=mt1
```

#### Facebook OAuth (for social login)
```env
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
FACEBOOK_REDIRECT_URL=https://yourdomain.com/auth/facebook/callback
```

**Setup Steps:**
1. Visit [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or use existing app
3. Go to **Facebook Login** → **Settings**
4. Add your domain to **Valid OAuth Redirect URIs**
5. Copy App ID and App Secret to your `.env`

## Security Considerations

### Production Environment

**Required Changes for Production:**
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com
LOG_LEVEL=error
MAIL_ENCRYPTION=tls
```

**Security Checklist:**
- [ ] Set `APP_DEBUG=false`
- [ ] Use HTTPS URLs
- [ ] Use strong database passwords
- [ ] Enable mail encryption (TLS/SSL)
- [ ] Use environment-specific reCAPTCHA keys
- [ ] Configure proper logging levels

### Sensitive Information

**Never commit these to git:**
- Database passwords
- Mail passwords
- API keys and secrets
- reCAPTCHA keys
- AWS credentials

## Troubleshooting

### Common Issues

**1. Application Key Missing**
```bash
php artisan key:generate
```

**2. Database Connection Failed**
- Verify database credentials
- Ensure MySQL service is running
- Check database exists

**3. Mail Not Sending**
- Verify SMTP credentials
- Check firewall/port restrictions
- Test with a simple mail client

**4. reCAPTCHA Not Working**
- Verify site key and secret key
- Check domain configuration in Google reCAPTCHA admin
- Ensure keys match the environment (test vs production)

### Testing Configuration

**Test database connection:**
```bash
php artisan tinker
DB::connection()->getPdo();
```

**Test mail configuration:**
```bash
php artisan tinker
Mail::raw('Test email', function($msg) { $msg->to('<EMAIL>')->subject('Test'); });
```

## Environment-Specific Examples

### Local Development
```env
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000
DB_HOST=127.0.0.1
MAIL_MAILER=log
```

### Staging
```env
APP_ENV=staging
APP_DEBUG=true
APP_URL=https://staging.yourdomain.com
MAIL_MAILER=smtp
```

### Production
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com
LOG_LEVEL=error
CACHE_DRIVER=redis
SESSION_DRIVER=redis
```
