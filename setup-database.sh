#!/bin/bash

echo "🚀 Setting up NeuronicLab Database..."

# Run essential migrations in order
echo "📦 Running essential migrations..."

# Core tables
php artisan migrate --path=database/migrations/2020_03_14_141017_create_languages_table.php --force
php artisan migrate --path=database/migrations/2021_02_18_105157_create_sessions_table.php --force
php artisan migrate --path=database/migrations/2016_04_22_211638_create_roles_table.php --force

# Content tables
php artisan migrate --path=database/migrations/2021_02_21_092726_create_settings_table.php --force
php artisan migrate --path=database/migrations/2021_03_02_124524_create_menus_table.php --force
php artisan migrate --path=database/migrations/2021_03_02_150833_create_sliders_table.php --force
php artisan migrate --path=database/migrations/2021_03_04_111731_create_services_table.php --force
php artisan migrate --path=database/migrations/2021_03_04_114538_create_testimonials_table.php --force
php artisan migrate --path=database/migrations/2021_03_04_132321_create_projects_table.php --force
php artisan migrate --path=database/migrations/2021_03_06_143051_create_project_categories_table.php --force

# Settings tables
php artisan migrate --path=database/migrations/2021_03_07_094913_create_header_footer_settings_table.php --force
php artisan migrate --path=database/migrations/2021_03_07_094936_create_home_settings_table.php --force

# Recent enhancements
php artisan migrate --path=database/migrations/2025_07_26_083857_add_enabled_to_languages_table.php --force
php artisan migrate --path=database/migrations/2025_07_26_093040_add_sort_order_to_services_table.php --force
php artisan migrate --path=database/migrations/2025_07_26_094238_add_sort_order_to_projects_table.php --force
php artisan migrate --path=database/migrations/2025_07_26_112844_add_media_type_to_projects_table.php --force
php artisan migrate --path=database/migrations/2025_07_26_113500_add_language_id_to_content_tables.php --force

echo "✅ Migrations completed!"

# Create essential data
echo "📝 Creating essential data..."

# Create default language
php artisan tinker --execute="
if (!App\Models\Language::where('code', 'en')->exists()) {
    App\Models\Language::create([
        'name' => 'English',
        'code' => 'en',
        'is_default' => 1,
        'rtl' => 0,
        'enabled' => 1
    ]);
    echo 'Default language created\n';
}
"

# Create default project category
php artisan tinker --execute="
if (!App\Models\ProjectCategory::where('name', 'General')->exists()) {
    App\Models\ProjectCategory::create(['name' => 'General']);
    echo 'Default project category created\n';
}
"

# Create default roles
php artisan tinker --execute="
use App\Models\Role;
if (!Role::where('name', 'admin')->exists()) {
    Role::create(['name' => 'admin']);
    Role::create(['name' => 'author']);
    Role::create(['name' => 'user']);
    echo 'Default roles created\n';
}
"

echo "✅ Essential data created!"

# Clear caches
echo "🧹 Clearing caches..."
php artisan config:clear
php artisan cache:clear
php artisan view:clear

echo "🎉 Database setup completed successfully!"
echo ""
echo "📋 Summary:"
echo "   - All essential tables created"
echo "   - Default language (English) added"
echo "   - Default project category added"
echo "   - Default roles (admin, author, user) added"
echo "   - YouTube video support enabled for projects"
echo ""
echo "🔗 You can now access:"
echo "   - Frontend: http://127.0.0.1:8000"
echo "   - Admin: http://127.0.0.1:8000/admin"
echo ""
