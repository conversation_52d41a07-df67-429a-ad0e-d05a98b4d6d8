<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Language;
use App\Models\ProjectCategory;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class EssentialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create default language if it doesn't exist
        if (!Language::where('code', 'en')->exists()) {
            Language::create([
                'name' => 'English',
                'code' => 'en',
                'is_default' => 1,
                'rtl' => 0,
                'enabled' => 1
            ]);
            
            $this->command->info('✓ Default English language created');
        }

        // Create default project category if it doesn't exist
        if (!ProjectCategory::where('name', 'General')->exists()) {
            ProjectCategory::create([
                'name' => 'General'
            ]);

            $this->command->info('✓ Default project category created');
        }

        // Create admin user if it doesn't exist
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'email_verified_at' => now()
            ]);
            
            $this->command->info('✓ Default admin user created (email: <EMAIL>, password: password)');
        }

        $this->command->info('Essential data seeding completed!');
    }
}
