<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Service;
use App\Models\Language;

class UpdateServicesSortOrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all languages
        $languages = Language::all();

        foreach ($languages as $language) {
            // Get services for this language ordered by ID
            $services = Service::where('language_id', $language->id)
                ->where('sort_order', 0)
                ->orderBy('id', 'ASC')
                ->get();

            // Update sort_order for each service
            foreach ($services as $index => $service) {
                $service->update(['sort_order' => $index + 1]);
            }
        }

        $this->command->info('Updated sort_order for all existing services.');
    }
}
