<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sliders', function (Blueprint $table) {
            // Add typed_text field if it doesn't exist
            if (!Schema::hasColumn('sliders', 'typed_text')) {
                $table->text('typed_text')->nullable()->after('heading2');
            }
            
            // Add bodyslider field if it doesn't exist
            if (!Schema::hasColumn('sliders', 'bodyslider')) {
                $table->text('bodyslider')->nullable()->after('typed_text');
            }
            
            // Add button_text2 field if it doesn't exist
            if (!Schema::hasColumn('sliders', 'button_text2')) {
                $table->string('button_text2')->nullable()->after('button_link');
            }
            
            // Add button_link2 field if it doesn't exist
            if (!Schema::hasColumn('sliders', 'button_link2')) {
                $table->string('button_link2')->nullable()->after('button_text2');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sliders', function (Blueprint $table) {
            $table->dropColumn(['typed_text', 'bodyslider', 'button_text2', 'button_link2']);
        });
    }
};
