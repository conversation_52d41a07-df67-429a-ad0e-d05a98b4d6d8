<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add language_id to sliders table
        if (Schema::hasTable('sliders') && !Schema::hasColumn('sliders', 'language_id')) {
            Schema::table('sliders', function (Blueprint $table) {
                $table->unsignedBigInteger('language_id')->default(1)->after('id');
                $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            });
        }

        // Add language_id to menus table
        if (Schema::hasTable('menus') && !Schema::hasColumn('menus', 'language_id')) {
            Schema::table('menus', function (Blueprint $table) {
                $table->unsignedBigInteger('language_id')->default(1)->after('id');
                $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            });
        }

        // Add language_id to services table
        if (Schema::hasTable('services') && !Schema::hasColumn('services', 'language_id')) {
            Schema::table('services', function (Blueprint $table) {
                $table->unsignedBigInteger('language_id')->default(1)->after('id');
                $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            });
        }

        // Add language_id to testimonials table
        if (Schema::hasTable('testimonials') && !Schema::hasColumn('testimonials', 'language_id')) {
            Schema::table('testimonials', function (Blueprint $table) {
                $table->unsignedBigInteger('language_id')->default(1)->after('id');
                $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            });
        }

        // Add language_id to projects table if it doesn't exist
        if (Schema::hasTable('projects') && !Schema::hasColumn('projects', 'language_id')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->unsignedBigInteger('language_id')->default(1)->after('id');
                $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            });
        }

        // Add language_id to posts table if it doesn't exist
        if (Schema::hasTable('posts') && !Schema::hasColumn('posts', 'language_id')) {
            Schema::table('posts', function (Blueprint $table) {
                $table->unsignedBigInteger('language_id')->default(1)->after('id');
                $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove language_id from all tables
        $tables = ['sliders', 'menus', 'services', 'testimonials', 'projects', 'posts'];
        
        foreach ($tables as $table) {
            if (Schema::hasTable($table) && Schema::hasColumn($table, 'language_id')) {
                Schema::table($table, function (Blueprint $table) {
                    $table->dropForeign(['language_id']);
                    $table->dropColumn('language_id');
                });
            }
        }
    }
};
