{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "anhskohbo/no-captcha": "^3.3", "astrotomic/laravel-translatable": "^11.9", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "^2.5", "laravel/fortify": "^1.25", "laravel/framework": "^11.0", "laravel/jetstream": "^5.3", "laravel/socialite": "^5.2", "laravel/tinker": "^2.5", "laravel/ui": "^4.6", "mews/purifier": "^3.3", "spatie/laravel-cookie-consent": "^3.3", "unisharp/laravel-filemanager": "^2.2"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "phpunit/phpunit": "^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "cache-dir": "/home/<USER>/neuroniclab.com/cache"}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"npm run watch\" --names=server,queue,assets --kill-others"], "dev-simple": "php artisan serve", "dev-assets": "npm run dev", "watch": "npm run watch", "hot": "npm run hot", "prod": "npm run production", "test": ["@php artisan config:clear --ansi", "@php artisan test"]}}