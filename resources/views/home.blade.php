@extends('layouts.front')

@section('title') {{$homesetting->meta_title}} @endsection
@section('meta') {{$homesetting->meta_description}} @endsection


@section('content')



    <div class="slider-darkmoon-section">
        <div class="slider-darkmoon owl-carousel">
            
            @php $count = 0; @endphp
            @foreach( $sliders as $slido )
            
            <div class="slider-inner-darkmoon"  data-background-image-url="{{$slido->photo ? '/images/media/' . $slido->photo->file : '/img/200x200.png'}}">
                
                <div class="container">
                   
                    <div class="slider-content">
                       <h1 @if( $count == 0 ) class="active" @endif>{!!$slido->heading1!!} </h1>
                       <h2 @if( $count == 0 ) class="active" @endif>{!!$slido->heading2!!}</h2>

                     

                       <div class="slider-body  @if( $count == 0 ) active @endif">
                           @if( $count == 0 && $slido->typed_text )
                               <p>Are you looking for <span class="slider_typed_text"></span> Neuronic Lab is a <strong>digital agency</strong> built with one purpose: to help you <strong>define your brand</strong>. We offer impeccable service combining a nice and user-friendly design with <strong>quality programming.</strong></p>
                           @else
                               {!!$slido->bodyslider!!}
                           @endif
                       </div>
                       
                        @if($slido->button_text)
                            <div class="button-slider-b">
                                <a href="{!!$slido->button_link!!}" target="_self" class="btn btn-slider"><span>{!!$slido->button_text!!}</span><svg width="11.4" height="9.2"> <use xlink:href="#arrow"></use></svg></a>
                            </div>
                        @endif

                        @if($slido->button_text2)
                            <div class="button-slider-b">
                                <a href="{!!$slido->button_link2!!}" target="_self" class="btn btn-slider"><span>{!!$slido->button_text2!!}</span><svg width="11.4" height="9.2"> <use xlink:href="#arrow"></use></svg></a>
                            </div>
                        @endif
                       
                    </div>
                </div>
            </div>

            @php $count++; @endphp

            @endforeach
        
        </div>

        <div class="header-social-share">
            {!!$headerfooter->social_links!!}
        </div>

        <a href="#" class="hero__scroll"><svg width="15" height="22.1"><use xlink:href="#scroll"></use></svg></a>
    </div>

    

    <div class="about-section light-section">
        <div class="container">
            <div class="row">
               
                <div class="col-md-6">
                    
              
                    <div class="item-about">

                        <div class="item-about-row">
                            <div class="item-about-img2">
                                <div class="avo-image avo-tooltip about-img3 big-paral">
                                    <div class="simpleParallax imago" data-tooltip-tit="{{$homesetting->about_image3_titlu1}}" data-tooltip-sub="{{$homesetting->about_image3_titlu2}}"><img src="/img/loading-blog.gif" width="500" height="666" class="lazy thumparallax-down img-fluid" data-src="{{$homesetting->about_image3}}" alt="about-us" /></div>
                                </div>
                            </div>
                            
                            <div class="item-about-img1">
                                <div class="avo-image avo-tooltip about-img1 big-paral">
                                    <div class="simpleParallax imago" data-tooltip-tit="{{$homesetting->about_image2_titlu1}}" data-tooltip-sub="{{$homesetting->about_image2_titlu2}}"><img src="/img/loading-blog.gif" width="500" height="666" class="lazy thumparallax-down img-fluid" data-src="{{$homesetting->about_image2}}" alt="about-us" /></div>
                                </div>
                                <div class="avo-image avo-tooltip about-img2 big-paral">
                                    <div class="simpleParallax imago" data-tooltip-tit="{{$homesetting->about_image1_titlu1}}" data-tooltip-sub="{{$homesetting->about_image1_titlu2}}"><img src="/img/loading-blog.gif" width="500" height="666" class="lazy thumparallax-down img-fluid" data-src="{{$homesetting->about_image1}}" alt="about-us" /></div>
                                </div>
                            </div>
                        </div>
                       

                        <div class="exp-about">
                            <h5 class="nmb-font-about">{{$homesetting->about_yearstitle}}</h5>
                            <h6 class="service_summary-about">{{$homesetting->about_yearstext}}</h6>
                        </div>

                    </div>

                 

                </div>


                <div class="col-md-6">

                    <h4 class="about-heading1-home">{!!$homesetting->about_subtitle!!}</h4>
                    <h3 class="about-heading2-home">{!!$homesetting->about_title!!}</h3>

                    {!!$homesetting->about_description!!}

                    <a href="{{$homesetting->about_buttonlink}}" target="_self" class="btn btn-style1"><span>{{$homesetting->about_buttontext}}</span><svg width="11.4" height="9.2"> <use xlink:href="#arrow"></use></svg></a>

                </div>


            </div>
        </div>
    </div>


    <div class="services-section">
        <div class="container">
            
            <h3>{!!$homesetting->services_title!!}</h3>

            <div class="description-services">{!!$homesetting->sevices_text!!}</div>

            <div class="service-boxes-slider owl-carousel">
                
                @foreach( $services as $service )

                <div class="card-parent">
                    <div class="card-inner-row">
                        <div class="card featured to-top-left">
                            <div class="heading-wrapper">
                                <h4 class="heading">{!!$service->icon!!} {{$service->title}}</h4>
                            </div> 
            
                            <div class="paragraph-wrapper">
                                <p class="paragraph">{{$service->description}}</p>
                            </div>

                            <div class="project-button">
                                <a href="{{$service->button_link}}" title="{{$service->title}}"><span>{{$service->button_text}}</span><svg viewBox="0 0 80 80"><polyline points="19.89 15.25 64.03 15.25 64.03 59.33"></polyline><line x1="64.03" y1="15.25" x2="14.03" y2="65.18"></line></svg></a>
                            </div>

                        </div>
                        <div class="card-img">
                            <img class="img-fluid project-image lazy" width="400" height="400" src="/img/loading-blog.gif " data-src="{{$service->photo ? '/images/media/' . $service->photo->file : '/img/200x200.png'}}" alt="{{$service->title}}">
                        </div>
                    </div>
                </div>
                @endforeach

            </div> 
     
        </div>
    </div>


     <div class="fun-facts-section light-section" id="fun-facts">
        <div class="container">

            <h3 class="fun-facts-heading1">{{$homesetting->fun_title}}</h3>

            <p>{{$homesetting->fun_description}}</p>

            <div class="row fun-facts-timer">
                <div class="col-md-3">
                    <div class="radial">
                        <div class="radial-icon">{!!$homesetting->count_icon1!!}</div>
                        <span class="timer" data-from="0" data-to="{{$homesetting->count_number1}}" data-speed="4000">{{$homesetting->count_number1}}</span>
                        <h4>{{$homesetting->count_description1}}</h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="radial">
                        <div class="radial-icon">{!!$homesetting->count_icon2!!}</div>
                        <span class="timer" data-from="0" data-to="{{$homesetting->count_number2}}" data-speed="4000">{{$homesetting->count_number2}}</span>
                        <h4>{{$homesetting->count_description2}}</h4>
                    </div>
                    
                </div>
                <div class="col-md-3">
                    <div class="radial">
                        <div class="radial-icon">{!!$homesetting->count_icon3!!}</div>
                        <span class="timer" data-from="0" data-to="{{$homesetting->count_number3}}" data-speed="4000">{{$homesetting->count_number3}}</span>
                        <h4>{{$homesetting->count_description3}}</h4>
                    </div>
                    
                </div>
                <div class="col-md-3">
                    <div class="radial">
                        <div class="radial-icon">{!!$homesetting->count_icon4!!}</div>
                        <span class="timer" data-from="0" data-to="{{$homesetting->count_number4}}" data-speed="4000">{{$homesetting->count_number4}}</span>
                        <h4>{{$homesetting->count_description4}}</h4>
                    </div>
                </div>
            </div>


        </div>
    </div>

     <div class="portfolio-section">
        <div class="container">
            <h4>{{$homesetting->projects_subtitle}}</h4>
            <h3>{!!$homesetting->projects_title!!}</h3>



            <div class="portfolio-slider owl-carousel">
                
                @foreach($projects as $key=>$project)
                    
                    <div class="portfolio-slider-inner">
                
                            <div class="project-box-div"> 
                                @php $count = $key + 1 @endphp
                                <a href="{{URL::to('/')}}/project/{{$project->slug}}" title="{{$project->title}}">
                                    @if($project->isVideoType())
                                        <div class="position-relative">
                                            <img class="img-fluid project-image lazy youtube-thumbnail"
                                                 width="400"
                                                 height="400"
                                                 src="/img/loading-blog.gif"
                                                 data-src="{{ $project->getYoutubeThumbnail() ?: '/img/200x200.png' }}"
                                                 data-video-id="{{ $project->getYoutubeVideoId() }}"
                                                 alt="{{$project->title}}"
                                                 onerror="handleYoutubeThumbnailError(this)">
                                            <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 48px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                                                <i class="fas fa-play-circle"></i>
                                            </div>
                                        </div>
                                    @else
                                        <img class="img-fluid project-image lazy" width="400" height="400" src="/img/loading-blog.gif " data-src="{{$project->photo ? '/images/media/' . $project->photo->file : '/img/200x200.png'}}" alt="{{$project->title}}">
                                    @endif
                                </a>
                                <div class="project-meta">
                                    <div class="project-number">
                                        <span>0{{$count}}</span>
                                    </div>
                                    <div class="project-category">
                                        <span class="block_text">{{$project->project_category->name}} </span>
                                    </div>
                                    <div class="project-meta-title">
                                        <a href="{{URL::to('/')}}/project/{{$project->slug}}" title="{{$project->title}}"><span class="project__text">{{$project->title}}</span></a>
                                    </div>
                                    <div class="project-button">
                                        <a href="{{URL::to('/')}}/project/{{$project->slug}}" title="{{$project->title}}"><span>{{clean( trans('meridian-backend.view_project') , array('Attr.EnableID' => true))}}</span><svg viewBox="0 0 80 80"><polyline points="19.89 15.25 64.03 15.25 64.03 59.33"></polyline><line x1="64.03" y1="15.25" x2="14.03" y2="65.18"></line></svg></a>
                                    </div>
                                </div>
                            </div>
                       
                    </div>

                    @if ($key == 3)
                        @break
                    @endif


                @endforeach

            
            </div>
        </div>
    </div>

    


    <div class="testimonial-section light-section">

        <div class="container">

            <h3>{{$homesetting->testimonial_title}}</h3>
            <p>{{$homesetting->testimonial_subtitle}}</p>

            <div class="testimonial-section-slider owl-carousel">

                @foreach($testimonials as $testimonial)
                <blockquote class="testimonial-slide">
                    <div class="testimonial-layout1">
                        <div class="item-figure">
                            <img class="img-fluid" width="90" height="90" src="{{$testimonial->profile_pic ? $testimonial->profile_pic : '/img/200x200.png'}}" alt="">
                        </div>
                        <div class="item-content">
                            <h3 class="item-title">{{$testimonial->name}}</h3>
                            <div class="item-sub-title">{{$testimonial->position}}</div>
                            <div class="item-paragraph">{!!$testimonial->description!!}</div>
                        </div>
                    </div>
                </blockquote>
                @endforeach

            </div>

        </div>

    </div>

    <div class="blog-section">

        <div class="container">

            <h3 class="blog-section-subtitle">{!!$homesetting->blog_subtitle!!}</h3>
            <h3 class="blog-section-title">{!!$homesetting->blog_title!!}</h3>

            <div class="row">

                @foreach($posts as $post)
                <div class="col-md-4">
                    <article class="blog-single-post">

                        <div class="after-bg">
                           <img class="lazy blog_post_image img-fluid" width="370" height="380" src="https://cdn.dribbble.com/users/105033/screenshots/1132714/loading-animation-800.gif" data-src="{{$post->photo ? '/images/media/' . $post->photo->file : '/img/200x200.png'}}" alt="{{$post->title}}">
                        </div>
                        <div class="blog-item">
                           <div class="box-content p-relative">
                              <div class="box-content-body">
                                <div class="entry-meta">
                                    <div class="entry-date"><span>  {{ date('M d, Y', strtotime($post->created_at)) }} </span></div>
                                </div>
                                <h2 class="title-block">
                                    <a href="{{URL::to('/')}}/post/{{$post->slug}}" title="{{$post->title}}">{{$post->title}}</a>
                                 </h2>
                                <div class="block-desc"><p>{{$post->meta_description}}</p></div>
                                <div class="project-button">
                                    <a href="{{URL::to('/')}}/post/{{$post->slug}}" title="{{$post->title}}"><span>{{clean( trans('meridian-backend.load_more') , array('Attr.EnableID' => true))}}</span><svg viewBox="0 0 80 80"><polyline points="19.89 15.25 64.03 15.25 64.03 59.33"></polyline><line x1="64.03" y1="15.25" x2="14.03" y2="65.18"></line></svg></a>
                                </div>
                              </div>
                           </div>
                        </div>

                    </article>
                </div>
                @endforeach

                
            </div>

        </div>

    </div>



@endsection

@section('scripts')
<script type="text/javascript">
    ( function ( $ ) {
        'use strict';
        $( document ).ready( function () {
            /* SLIDER TYPED TEXT */
            $(function(){
                @if($sliders->count() > 0 && $sliders->first()->typed_text)
                    $(".slider_typed_text").typed({
                      strings: {!! $sliders->first()->typed_text !!},
                      typeSpeed: 1,
                      backDelay: 4000,
                      loop: true
                    });
                @else
                    // Fallback test data
                    $(".slider_typed_text").typed({
                      strings: ["App Development?", "Web Development?", "Digital Solutions?"],
                      typeSpeed: 1,
                      backDelay: 4000,
                      loop: true
                    });
                @endif
            });
        })
    } ( jQuery ) )
</script>
@endsection

