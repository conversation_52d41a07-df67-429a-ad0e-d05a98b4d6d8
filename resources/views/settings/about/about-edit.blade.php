@extends('layouts.admin')

@section('content')

@include('includes.tinyeditor')

<!-- Begin Page Content -->
<div class="container-fluid">


    <!-- Page Heading -->
    <h1 class="h3 mb-2 text-gray-800">{{clean( trans('meridian-backend.about_settings') , array('Attr.EnableID' => true))}}</h1>

                @if ($message = Session::get('setting_success'))
                    <div class="alert alert-success alert-block">
                        <button type="button" class="close" data-dismiss="alert"><i class="fas fa-times"></i></button>    
                        <strong>{{ $message }}</strong>
                    </div>
                @endif

                <div class="pb-2 text-right">
                    @if (!empty($langs))
                        <select name="language" class="form-control language-control" onchange="window.location='{{url()->current() . '?language='}}'+this.value">
                            <option value="" selected disabled>{{clean( trans('meridian-backend.select_language') , array('Attr.EnableID' => true))}}</option>
                            @foreach ($langs as $lang)
                                <option value="{{$lang->code}}" {{$lang->code == request()->input('language') ? 'selected' : ''}}>{{$lang->name}}</option>
                            @endforeach
                        </select>
                    @endif
                </div>


                @include('includes.form-errors')

                <div class="row">

                	<div class="col-md-12">



                        <!-- about -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-dark">{{clean( trans('meridian-backend.about_settings') , array('Attr.EnableID' => true))}}</h6>
                            </div>
                            <div class="card-body">
                        		<form action="{{route('about-setting.update', $setting->id)}}" method="POST" enctype="multipart/form-data">
        					        @csrf
        					        @method('PUT')

        					        <div class="row">
        					   
        	                           
                                       <div class="col-xs-12 col-sm-12 col-md-12">

                                             <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <strong>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}}</strong> <span>{{clean( trans('meridian-backend.upload_image') , array('Attr.EnableID' => true))}} <a target="_blank" href="{{route('media.create') . '?language=' . request()->input('language')}}"> {{clean( trans('meridian-backend.here') , array('Attr.EnableID' => true))}} </a> {{clean( trans('meridian-backend.then_copy_url') , array('Attr.EnableID' => true))}} <a target="_blank" href="{{route('media.index'). '?language=' . request()->input('language')}}"> {{clean( trans('meridian-backend.here') , array('Attr.EnableID' => true))}} </a></span>
                                                        <br>
                                                        <img style="padding-bottom:10px" class="img-fluid" width="300" src="{{$setting->banner_img ? $setting->banner_img : '/img/200x200.png'}}" alt="123">
                                                        <input type="text" name="banner_img" class="form-control" value="{{$setting->banner_img}}">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <strong>{{clean( trans('meridian-backend.title') , array('Attr.EnableID' => true))}}</strong>
                                                        <input type="text" name="banner_title" class="form-control" value="{{$setting->banner_title}}">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <strong>{{clean( trans('meridian-backend.description') , array('Attr.EnableID' => true))}}</strong>
                                                        <input type="text" name="banner_desc" class="form-control" value="{{$setting->banner_desc}}">
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <strong>{{clean( trans('meridian-backend.subtitle') , array('Attr.EnableID' => true))}}</strong>
                                                        <input type="text" name="about_subtitle" class="form-control" value="{{$setting->about_subtitle}}">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <strong>{{clean( trans('meridian-backend.title') , array('Attr.EnableID' => true))}}</strong>
                                                        <input type="text" name="about_title" class="form-control" value="{{$setting->about_title}}">
                                                    </div>
                                                </div>
                                            </div>




                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.description') , array('Attr.EnableID' => true))}}</strong>

                                                                                        <div id="editor-container" style="height: 300px; border: 1px solid #ccc;"></div>


<div id="image-editor-modal" style="display: none; position: fixed; top: 10%; left: 50%; transform: translateX(-50%); width: 80%; height: 60%; background: #fff; border: 1px solid #ccc; z-index: 9999; padding: 20px;">
  <label for="image-src">Image URL:</label>
  <input type="text" id="image-src" style="width: 100%;" />
  <label for="image-alt">Alt Text:</label>
  <input type="text" id="image-alt" style="width: 100%;" />
  <button id="save-image" style="margin-top: 10px;">Save</button>
  <button id="cancel-image" style="margin-top: 10px;">Cancel</button>
</div>


<div id="link-editor-modal" style="display: none; position: fixed; top: 10%; left: 50%; transform: translateX(-50%); width: 80%; height: 60%; background: #fff; border: 1px solid #ccc; z-index: 9999; padding: 20px;">
  <label for="link-url">Link URL:</label>
  <input type="text" id="link-url" style="width: 100%;" />
  <label for="link-text">Link Text:</label>
  <input type="text" id="link-text" style="width: 100%;" />
  <button id="save-link" style="margin-top: 10px;">Save</button>
  <button id="cancel-link" style="margin-top: 10px;">Cancel</button>
</div>
                                                
                                                <textarea name="about_description" class="form-control" rows="6"  id="body"  style="display:none;">{{clean( $setting->about_description , array('Attr.EnableID' => true))}}</textarea>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <strong>{{clean( trans('meridian-backend.button_text') , array('Attr.EnableID' => true))}}</strong>
                                                        <input type="text" name="about_buttontext" class="form-control" value="{{$setting->about_buttontext}}">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <strong>{{clean( trans('meridian-backend.button_link') , array('Attr.EnableID' => true))}}</strong>
                                                        <input type="text" name="about_buttonlink" class="form-control" value="{{$setting->about_buttonlink}}">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <strong>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}}</strong>
                                                        <input type="text" name="about_image" class="form-control" value="{{$setting->about_image}}">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <strong>{{clean( trans('meridian-backend.button_youtube_link') , array('Attr.EnableID' => true))}}</strong>
                                                        <input type="text" name="about_ytlink" class="form-control" value="{{$setting->about_ytlink}}">
                                                    </div>
                                                </div>
                                            </div>

                                        </div>

        					            <div class="col-xs-12 col-sm-12 col-md-12 text-right">
        					                <button type="submit" class="btn btn-primary">{{clean( trans('meridian-backend.update') , array('Attr.EnableID' => true))}}</button>
        					            </div>
        					        </div>

        					    </form>
                            </div>
                        </div>
                        <!-- about -->

                        <!-- member -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-dark">{{clean( trans('meridian-backend.section_members') , array('Attr.EnableID' => true))}}</h6>
                            </div>
                            <div class="card-body">
                                <form action="{{route('about-setting.update', $setting->id)}}" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    @method('PUT')


                                    <div class="form-group">
                                        <strong>{{clean( trans('meridian-backend.title') , array('Attr.EnableID' => true))}}</strong>
                                        <input type="text" name="member_title_section" class="form-control" value="{{$setting->member_title_section}}">
                                    </div>
                           

                                    <div class="col-xs-12 col-sm-12 col-md-12 text-right">
                                        <button type="submit" class="btn btn-primary">{{clean( trans('meridian-backend.update') , array('Attr.EnableID' => true))}}</button>
                                    </div>

                                </form>
                                <div class="mb-3">
                                    <a class="btn btn-primary" href="{{ route('member.index') . '?language=' . request()->input('language')}}">{{clean( trans('meridian-backend.view_all') , array('Attr.EnableID' => true))}}</a>
                                    <a class="btn btn-primary" href="{{ route('member.create') . '?language=' . request()->input('language')}}">{{clean( trans('meridian-backend.create') , array('Attr.EnableID' => true))}}</a>
                                </div>
                            </div>
                        </div>
                        <!-- member -->

                        <!-- testimonial -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-dark">{{clean( trans('meridian-backend.section_6_testimonials') , array('Attr.EnableID' => true))}}</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <a class="btn btn-primary" href="{{ route('testimonial.index') . '?language=' . request()->input('language')}}">{{clean( trans('meridian-backend.view_all') , array('Attr.EnableID' => true))}}</a>
                                    <a class="btn btn-primary" href="{{ route('testimonial.create') . '?language=' . request()->input('language')}}">{{clean( trans('meridian-backend.create') , array('Attr.EnableID' => true))}}</a>
                                </div>
                            </div>
                        </div>
                        <!-- testimonial -->

                        <!-- clients -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-dark">{{clean( trans('meridian-backend.section_clients') , array('Attr.EnableID' => true))}}</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <a class="btn btn-primary" href="{{ route('client.index') . '?language=' . request()->input('language')}}">{{clean( trans('meridian-backend.view_all') , array('Attr.EnableID' => true))}}</a>
                                    <a class="btn btn-primary" href="{{ route('client.create') . '?language=' . request()->input('language')}}">{{clean( trans('meridian-backend.create') , array('Attr.EnableID' => true))}}</a>
                                </div>
                            </div>
                        </div>
                        <!-- clients -->

                        <!-- SEO -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-dark">{{clean( trans('meridian-backend.seo') , array('Attr.EnableID' => true))}}</h6>
                            </div>
                            <div class="card-body">
                                <form action="{{route('about-setting.update', $setting->id)}}" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    @method('PUT')

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.meta_title') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="meta_title" class="form-control" value="{{$setting->meta_title}}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.meta_description') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="meta_description" class="form-control" value="{{$setting->meta_description}}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.slug') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="slug" class="form-control" value="{{$setting->slug}}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.anchor_text') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="breadcrumbs_anchor" class="form-control" value="{{$setting->breadcrumbs_anchor}}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-xs-12 col-sm-12 col-md-12 text-right">
                                            <button type="submit" class="btn btn-primary">{{clean( trans('meridian-backend.update') , array('Attr.EnableID' => true))}}</button>
                                        </div>
                                    </div>

                                </form>

                            </div>
                        </div>
                        <!-- SEO -->


                		
                	</div>
                </div>



</div>
<!-- /.container-fluid -->




@endsection