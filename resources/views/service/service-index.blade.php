@extends('layouts.admin')

@section('content')

<!-- Begin Page Content -->
<div class="container-fluid">


    <!-- Page Heading -->
    <h1 class="h3 mb-2 text-gray-800">{{clean( trans('meridian-backend.section_4_services') , array('Attr.EnableID' => true))}}</h1>

    <!-- DataTales Example -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{clean( trans('meridian-backend.section_4_services') , array('Attr.EnableID' => true))}}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">

                <div class="row">
                    <div class="col-lg-6">
                        <a href="{{route('home-setting.edit') .  '?language=' . request()->input('language')}}" class="btn btn-primary btn-back">Back to home</a>
                        <a href="{{route('service.create') . '?language=' . request()->input('language')}}" class="btn btn-primary btn-back">Create service</a>
                    </div>

                    <div class="col-lg-6 text-right">
                        @if (!empty($langs))
                            <select name="language" class="form-control language-control" onchange="window.location='{{url()->current() . '?language='}}'+this.value">
                                <option value="" selected disabled>{{clean( trans('meridian-backend.select_language') , array('Attr.EnableID' => true))}}</option>
                                @foreach ($langs as $lang)
                                    <option value="{{$lang->code}}" {{$lang->code == request()->input('language') ? 'selected' : ''}}>{{$lang->name}}</option>
                                @endforeach
                            </select>
                        @endif
                    </div>
                </div>

                @if ($message = Session::get('service_success'))
                    <div class="alert alert-success alert-block">
                        <button type="button" class="close" data-dismiss="alert"><i class="fas fa-times"></i></button>
                        <strong>{{ $message }}</strong>
                    </div>
                @endif

                <!-- Reorder Success/Error Messages -->
                <div id="reorder-message" class="alert" style="display: none;">
                    <button type="button" class="close" onclick="$('#reorder-message').hide()"><i class="fas fa-times"></i></button>
                    <span id="reorder-message-text"></span>
                </div>

                <!-- Drag and Drop Instructions -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> <strong>Tip:</strong> Drag and drop the rows below to reorder services. Changes will be saved automatically.
                </div>
               

                <form action="{{route('delete.service')}}" method="POST" class="form-inline">
                @csrf
                @method('DELETE')
                <div class="form-group">
                    <select name="checkbox_array" id="" class="form-control">
                        <option value="">{{clean( trans('meridian-backend.delete') , array('Attr.EnableID' => true))}}</option>
                    </select>
                </div>

                <div class="form-group">
                    <input type="submit" name="delete_all" class="btn btn-primary">
                </div>



                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="30"><i class="fas fa-arrows-alt" title="Drag to reorder"></i></th>
                            <th><input type="checkbox" id="options"></th>
                            <th>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}}</th>
                            <th>{{clean( trans('meridian-backend.box_icon') , array('Attr.EnableID' => true))}}</th>
                            <th>{{clean( trans('meridian-backend.title') , array('Attr.EnableID' => true))}}</th>
                            <th>{{clean( trans('meridian-backend.description') , array('Attr.EnableID' => true))}}</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th><i class="fas fa-arrows-alt"></i></th>
                            <th><input type="checkbox" id="options1"></th>
                            <th>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}}</th>
                            <th>{{clean( trans('meridian-backend.box_icon') , array('Attr.EnableID' => true))}}</th>
                            <th>{{clean( trans('meridian-backend.title') , array('Attr.EnableID' => true))}}</th>
                            <th>{{clean( trans('meridian-backend.description') , array('Attr.EnableID' => true))}}</th>
                        </tr>
                    </tfoot>
                    <tbody id="sortable-services">
                        @if($services)
                            @foreach($services as $service)
                                <tr data-id="{{$service->id}}" class="sortable-row">
                                    <td class="drag-handle" style="cursor: move; text-align: center; vertical-align: middle;">
                                        <i class="fas fa-grip-vertical text-muted"></i>
                                    </td>
                                    <td><input class="checkboxes" type="checkbox" name="checkbox_array[]" value="{{$service->id}}"></td>
                                    <td><img height="100" src="{{$service->photo ? '/images/media/' . $service->photo->file : '/img/200x200.png'}}" alt="">
                                    <p class="mb-0 mt-2"><a href="{{ route('service.edit', $service->id) . '?language=' . request()->input('language') }}">{{clean( trans('meridian-backend.edit') , array('Attr.EnableID' => true))}}</a></p>
                                    </td>

                                    <td data-label="link">{!!$service->icon!!}</td>
                                    <td data-label="link">{{$service->title}}</td>
                                    <td data-label="link">{{$service->description}}</td>
                                </tr>
                             @endforeach
                        @endif


                        
                    </tbody>
                </table>

                </form>

            </div>
        </div>
    </div>

</div>
<!-- /.container-fluid -->

@stop

@section('styles')
<style>
    .sortable-row {
        transition: all 0.3s ease;
    }

    .sortable-row.sortable-ghost {
        opacity: 0.4;
        background-color: #f8f9fa;
    }

    .sortable-row.sortable-chosen {
        background-color: #e3f2fd;
    }

    .drag-handle:hover {
        background-color: #f8f9fa;
    }

    .sorting-active {
        cursor: grabbing !important;
    }

    #reorder-message.alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    #reorder-message.alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }
</style>
@endsection

@section('footer')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Sortable
    const sortableElement = document.getElementById('sortable-services');

    if (sortableElement) {
        const sortable = Sortable.create(sortableElement, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onStart: function(evt) {
                $('body').addClass('sorting-active');
            },
            onEnd: function(evt) {
                $('body').removeClass('sorting-active');

                // Get the new order
                const services = [];
                $('#sortable-services tr').each(function(index) {
                    const serviceId = $(this).data('id');
                    if (serviceId) {
                        services.push({
                            id: serviceId,
                            sort_order: index + 1
                        });
                    }
                });

                // Send AJAX request to update order
                if (services.length > 0) {
                    updateServiceOrder(services);
                }
            }
        });
    }

    function updateServiceOrder(services) {
        $.ajax({
            url: '{{ route("service.reorder") }}',
            method: 'POST',
            data: {
                services: services,
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                showMessage('Updating order...', 'info');
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                } else {
                    showMessage(response.message || 'Failed to update order', 'danger');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Failed to update order';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showMessage(errorMessage, 'danger');

                // Reload page to restore original order
                setTimeout(function() {
                    location.reload();
                }, 2000);
            }
        });
    }

    function showMessage(message, type) {
        const messageDiv = $('#reorder-message');
        const messageText = $('#reorder-message-text');

        // Remove existing alert classes
        messageDiv.removeClass('alert-success alert-danger alert-info');

        // Add appropriate class
        messageDiv.addClass('alert-' + type);

        // Set message text
        messageText.text(message);

        // Show message
        messageDiv.show();

        // Auto-hide success messages after 3 seconds
        if (type === 'success') {
            setTimeout(function() {
                messageDiv.fadeOut();
            }, 3000);
        }
    }
});
</script>
@endsection

