@extends('layouts.front')


@section('title') {{$portfoliosettings->meta_title}} @endsection
@section('meta') {{$portfoliosettings->meta_description}} @endsection


@section('content')
  
  
   <div class="banner-section" data-background-image-url="{{$portfoliosettings->banner_img ? $portfoliosettings->banner_img : '/img/200x200.png'}}">

        <div class="container">
            <h1 class="banner-title">{!!$portfoliosettings->banner_title!!}</h1>
            <p class="banner-desc">{!!$portfoliosettings->banner_desc!!}</p>
        </div>

        <div class="header-social-share">
            {!!$headerfooter->social_links!!}
        </div>

        <a href="#" class="hero__scroll"><svg width="15" height="22.1"><use xlink:href="#scroll"></use></svg></a>
       
   </div>

   <div class="portfolio-section-page light-section">
       <div class="projects-page-row">

                      @php $count = 1; @endphp  
                      @foreach($projects as $project)
        

                        <div class="project-row @php if($count % 2 == 0){ echo 'project-row-right'; } @endphp">
                            <div class="project_index">0.@php echo $count; @endphp  </div>
                            <div class="project__img">
                                @if($project->isVideoType())
                                    <a href="{{URL::to('/')}}/project/{{$project->slug}}" title="{{$project->title}}" class="project-link">
                                        <div class="youtube-video-container"
                                             role="button"
                                             tabindex="0"
                                             aria-label="View project: {{ $project->title }}"
                                             data-project-url="{{URL::to('/')}}/project/{{$project->slug}}"
                                             style="position: relative; width: 100%; height: 100%; cursor: pointer;">
                                            <img class="img-fluid thumparallax-down youtube-thumbnail"
                                                 width="900"
                                                 height="938"
                                                 src="{{ $project->getYoutubeThumbnail() ?: '/img/200x200.png' }}"
                                                 alt="Video thumbnail for {{ $project->title }}"
                                                 data-video-id="{{ $project->getYoutubeVideoId() }}"
                                                 onerror="handleYoutubeThumbnailError(this)"
                                                 loading="lazy">
                                            <div class="video-play-overlay"
                                                 aria-hidden="true"
                                                 style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 72px; text-shadow: 2px 2px 4px rgba(0,0,0,0.8); z-index: 10; pointer-events: none;">
                                                <i class="fas fa-play-circle"></i>
                                            </div>
                                            <div class="video-overlay"
                                                 style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.1); transition: background 0.3s ease; z-index: 5;"></div>
                                        </div>
                                    </a>
                                @else
                                    <a href="{{URL::to('/')}}/project/{{$project->slug}}" title="{{$project->title}}" class="project-link">
                                        <img class="img-fluid thumparallax-down" width="900" height="938" src="{{$project->image_featured2 ? $project->image_featured2 : '/img/200x200.png'}}" alt="{{$project->title}}" loading="lazy">
                                    </a>
                                @endif
                            </div>
                            <div class="container">
                                <div class="info-row__info">
                                    <span class="case_tt">{{$project->project_category->name}}</span>
                                    <h2 class="info-row__title"><a href="{{URL::to('/')}}/project/{{$project->slug}}">{{$project->title}}</a></h2>
                                    <div class="project-desc">
                                        {!!$project->excerpt!!}
                                    </div>
                                    <div class="project-button">
                                        <a href="{{URL::to('/')}}/project/{{$project->slug}}" title="{{$project->title}}"><span>{{clean( trans('meridian-backend.view_project') , array('Attr.EnableID' => true))}}</span><svg viewBox="0 0 80 80"><polyline points="19.89 15.25 64.03 15.25 64.03 59.33"></polyline><line x1="64.03" y1="15.25" x2="14.03" y2="65.18"></line></svg></a>
                                    </div>
                                </div>
                            </div>
                        </div>


                        
                        @php $count++; @endphp  
                        @endforeach

       </div>
   </div>

 

@endsection





