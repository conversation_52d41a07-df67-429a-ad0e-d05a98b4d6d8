

@extends('layouts.admin')

@section('content')

@include('includes.tinyeditor')

<!-- Begin Page Content -->
<div class="container-fluid">


    <!-- Page Heading -->
    <h1 class="h3 mb-2 text-gray-800">{{clean( trans('meridian-backend.edit_testimonial') , array('Attr.EnableID' => true))}}</h1>

    <!-- DataTales Example -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{clean( trans('meridian-backend.edit_testimonial') , array('Attr.EnableID' => true))}}</h6>
        </div>
        <div class="card-body">

                <a href="{{route('testimonial.index') . '?language=' . request()->input('language')}}" class="btn btn-primary btn-back">{{clean( trans('meridian-backend.back_testimonial') , array('Attr.EnableID' => true))}}</a>

                @if ($message = Session::get('testimonial_success'))
                    <div class="alert alert-success alert-block">
                        <button type="button" class="close" data-dismiss="alert"><i class="fas fa-times"></i></button>    
                        <strong>{{ $message }}</strong>
                    </div>
                @endif


                @include('includes.form-errors')

                <div class="row">

                	<div class="col-md-12">

                		<form action="{{route('testimonial.update', $testimonial->id)}}" method="POST" enctype="multipart/form-data">
					        @csrf
					        @method('PUT')

					        <div class="row">

                                <div class="col-xs-12 col-sm-12 col-md-12">

                                    <div class="row">
                                        
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.name') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="name" class="form-control" value="{{$testimonial->name}}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.position') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="position" class="form-control" value="{{$testimonial->position}}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}} <span>{{clean( trans('meridian-backend.upload_image') , array('Attr.EnableID' => true))}} <a target="_blank" href="{{route('media.create')}}"> {{clean( trans('meridian-backend.here') , array('Attr.EnableID' => true))}} </a> {{clean( trans('meridian-backend.then_copy_url') , array('Attr.EnableID' => true))}} <a target="_blank" href="{{route('media.index')}}"> {{clean( trans('meridian-backend.here') , array('Attr.EnableID' => true))}} </a></span></strong>
                                                @if($testimonial->profile_pic)
                                                <img style="padding-bottom:10px" class="img-fluid" width="50" src="{{$testimonial->profile_pic}}" alt="">
                                                @endif
                                                <input type="text" name="profile_pic" class="form-control" value="{{$testimonial->profile_pic}}">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    
                                    
                                    
                                    <div class="form-group">
                                        <strong>{{clean( trans('meridian-backend.description') , array('Attr.EnableID' => true))}}</strong>

                                        <div id="editor-container" style="height: 300px; border: 1px solid #ccc;"></div>


<div id="image-editor-modal" style="display: none; position: fixed; top: 10%; left: 50%; transform: translateX(-50%); width: 80%; height: 60%; background: #fff; border: 1px solid #ccc; z-index: 9999; padding: 20px;">
  <label for="image-src">Image URL:</label>
  <input type="text" id="image-src" style="width: 100%;" />
  <label for="image-alt">Alt Text:</label>
  <input type="text" id="image-alt" style="width: 100%;" />
  <button id="save-image" style="margin-top: 10px;">Save</button>
  <button id="cancel-image" style="margin-top: 10px;">Cancel</button>
</div>


<div id="link-editor-modal" style="display: none; position: fixed; top: 10%; left: 50%; transform: translateX(-50%); width: 80%; height: 60%; background: #fff; border: 1px solid #ccc; z-index: 9999; padding: 20px;">
  <label for="link-url">Link URL:</label>
  <input type="text" id="link-url" style="width: 100%;" />
  <label for="link-text">Link Text:</label>
  <input type="text" id="link-text" style="width: 100%;" />
  <button id="save-link" style="margin-top: 10px;">Save</button>
  <button id="cancel-link" style="margin-top: 10px;">Cancel</button>
</div>

                                        <textarea name="description" class="form-control" rows="6" id="body"  style="display:none;">{{clean( $testimonial->description , array('Attr.EnableID' => true))}} </textarea>
                                    </div>


                                </div>
					   
	                           
                   

					            <div class="col-xs-12 col-sm-12 col-md-12 text-right">
					                <button type="submit" class="btn btn-primary">{{clean( trans('meridian-backend.update') , array('Attr.EnableID' => true))}}</button>
					            </div>
					        </div>

					    </form>
                		
                	</div>
                </div>

        </div>
    </div>

</div>
<!-- /.container-fluid -->




@endsection
