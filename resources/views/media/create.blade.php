@extends('layouts.admin')
@section('styles')
<link href="{{asset('css/libs/dropzone.min.css')}}" rel="stylesheet">
@stop
@section('content')
<!-- Begin Page Content -->
<div class="container-fluid">


    <!-- Page Heading -->
    <h1 class="h3 mb-2 text-gray-800">{{clean( trans('meridian-backend.upload_image') , array('Attr.EnableID' => true))}}</h1>

    <!-- DataTales Example -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{clean( trans('meridian-backend.upload_image') , array('Attr.EnableID' => true))}}</h6>
        </div>
        <div class="card-body">


            <a href="{{ route('media.index') }}" class="btn btn-primary btn-back">{{clean( trans('meridian-backend.back_media') , array('Attr.EnableID' => true))}}</a>

            <div class="table-responsive">
                <form action="{{route('media.store')}}" class="dropzone" method="POST" enctype="multipart/form-data">
                    @csrf

                </form>
            </div>


            <p class="mb-4 mt-4">{{clean( trans('meridian-backend.accepted_files') , array('Attr.EnableID' => true))}}</p>  

        </div>
    </div>

</div>
@stop

@section('footer')

    <!-- <script src="{{ asset('js/libs/dropzone.min.js') }}"></script>
    <script type="text/javascript">
        Dropzone.autoDiscover = false;
        var myDropzone = new Dropzone(".dropzone", { 
           maxFilesize: 10,
           acceptedFiles: ".jpeg,.jpg,.png,.gif,.webp,.svg"
        });
    </script> -->

@stop