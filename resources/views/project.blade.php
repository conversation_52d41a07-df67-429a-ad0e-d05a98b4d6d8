@extends('layouts.front')

@section('title') {{$project->meta_title}} @endsection
@section('meta') {{$project->meta_description}} @endsection

@section('styles')
<link href="{{ asset('css/front/magnific.min.css')}}" type="text/css" rel="stylesheet">
@endsection

@section('content')
  
  
   <div class="breadcrumb-area">
   	<div class="container">
   		 <h1 class="breadcrumb-title">{{$project->meta_title}}</h1>

   		<ul class="page-list">
            <li class="item-home"><a class="bread-link" href="{{ route('home') }}" title="Home">{{clean( trans('meridian-backend.home') , array('Attr.EnableID' => true))}}</a></li>
            <li class="separator separator-home"></li>
            <li class="item-home"><a class="bread-link" href="{{ route('portfolio') }}" title="Home">{{clean( trans('meridian-backend.our_projects') , array('Attr.EnableID' => true))}}</a></li>
            <li class="separator separator-home"></li>
            <li class="item-current">{{$project->meta_title}}</li>
        </ul>
   	</div>
   </div>

   <div class="project-content">

   		

   		<div class="container">

   			<div class="project__img_single">
                @if($project->isVideoType())
                    <div class="youtube-container">
                        <iframe src="{{ $project->getYoutubeEmbedUrl() }}" frameborder="0" allowfullscreen></iframe>
                    </div>
                @else
                    <img class="img-fluid thumparallax-down" width="900" height="938" src="{{$project->image_featured2 ? $project->image_featured2 : '/img/200x200.png'}}" alt="{{$project->title}}">
                @endif
	       	</div>

   			<div class="row">
					<div class="col-md-8">
				        <h2 class="post-name">{{$project->meta_title}}</h2>
				        <span class="darkmoon-animate-border"></span>
				        {!!$project->body!!}
					</div>
				    <div class="col-md-4">
				        <h4 class="post-name">{{clean( trans('meridian-backend.info') , array('Attr.EnableID' => true))}}</h4>
				        <span class="darkmoon-animate-border"></span>
				        
				        <p><strong>{{$project->date}}</strong></p>
				        <p><strong>{{$project->client}}</strong></p>
				        <p><strong>{{$project->project_category->name}}</strong></p>

				        <a href="{{$project->button_link}}" target="_blank" class="btn btn-style1"><span>{{$project->button_text}}</span></a>
				    </div>
				</div>

				<div class="gallery">
					<div class="row">

						<div class="col-md-6">
							<div class="featured-image">
								<a href="{{$project->img_gal1}}">
									<img class="thumparallax-down img-fluid" src="{{$project->img_gal1 ? $project->img_gal1 : '/img/200x200.png'}}" alt="{{$project->title}} Gallery 1">
								</a>
							</div>
						</div>

						<div class="col-md-6">
							<div class="featured-image">
								<a href="{{$project->img_gal2}}">
									<img class="thumparallax-down img-fluid" src="{{$project->img_gal2 ? $project->img_gal2 : '/img/200x200.png'}}" alt="{{$project->title}} Gallery 2">
								</a>
							</div>
						</div>

						<div class="col-md-6">
							<div class="featured-image">
								<a href="{{$project->img_gal3}}">
									<img class="thumparallax-down img-fluid" src="{{$project->img_gal3 ? $project->img_gal3 : '/img/200x200.png'}}" alt="{{$project->title}} Gallery 3">
								</a>
							</div>
						</div>
						
						<div class="col-md-6">
							<div class="featured-image">
								<a href="{{$project->img_gal4}}">
									<img class="thumparallax-down img-fluid" src="{{$project->img_gal4 ? $project->img_gal4 : '/img/200x200.png'}}" alt="{{$project->title}} Gallery 4">
								</a>
							</div>
						</div>

					</div>
					
				</div>

   		</div>
   		
   	</div>



@endsection

