@extends('layouts.admin')

@section('content')

<!-- Begin Page Content -->
<div class="container-fluid">


    <!-- Page Heading -->
    <h1 class="h3 mb-2 text-gray-800">{{clean( trans('meridian-backend.section_projects') , array('Attr.EnableID' => true))}}</h1>

    <!-- DataTales Example -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{clean( trans('meridian-backend.section_projects') , array('Attr.EnableID' => true))}}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">

                <div class="row">
                    <div class="col-lg-6">
                        <a href="{{route('home-setting.edit') . '?language=' . request()->input('language')}}" class="btn btn-primary btn-back">{{clean( trans('meridian-backend.back_homepage') , array('Attr.EnableID' => true))}}</a>
                        <a href="{{route('portfolio-setting.edit') . '?language=' . request()->input('language')}}" class="btn btn-primary btn-back">{{clean( trans('meridian-backend.back_projectpage') , array('Attr.EnableID' => true))}}</a>
                        <a href="{{route('project.create') . '?language=' . request()->input('language')}}" class="btn btn-primary btn-back">{{clean( trans('meridian-backend.create') , array('Attr.EnableID' => true))}}</a>
                    </div>

                    <div class="col-lg-6 text-right">
                        @if (!empty($langs))
                            <select name="language" class="form-control language-control" onchange="window.location='{{url()->current() . '?language='}}'+this.value">
                                <option value="" selected disabled>{{clean( trans('meridian-backend.select_language') , array('Attr.EnableID' => true))}}</option>
                                @foreach ($langs as $lang)
                                    <option value="{{$lang->code}}" {{$lang->code == request()->input('language') ? 'selected' : ''}}>{{$lang->name}}</option>
                                @endforeach
                            </select>
                        @endif
                    </div>
                </div>

                @if ($message = Session::get('project_success'))
                    <div class="alert alert-success alert-block">
                        <button type="button" class="close" data-dismiss="alert"><i class="fas fa-times"></i></button>
                        <strong>{{ $message }}</strong>
                    </div>
                @endif

                <!-- Reorder message -->
                <div id="reorder-message" class="alert" style="display: none;">
                    <span id="reorder-message-text"></span>
                </div>
               

                <form action="{{route('delete.project')}}" method="POST" class="form-inline">
                @csrf
                @method('DELETE')
                <div class="form-group">
                    <select name="checkbox_array" id="" class="form-control">
                        <option value="">{{clean( trans('meridian-backend.delete') , array('Attr.EnableID' => true))}}</option>
                    </select>
                </div>

                <div class="form-group">
                    <input type="submit" name="delete_all" class="btn btn-primary">
                </div>



                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th><i class="fas fa-arrows-alt"></i></th>
                            <th><input type="checkbox" id="options"></th>
                            <th scope="col">{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}}</th>
                            <th scope="col">{{clean( trans('meridian-backend.owner') , array('Attr.EnableID' => true))}}</th>
                            <th scope="col">{{clean( trans('meridian-backend.title') , array('Attr.EnableID' => true))}}</th>
                            <th scope="col">{{clean( trans('meridian-backend.category') , array('Attr.EnableID' => true))}}</th>
                            <th scope="col">{{clean( trans('meridian-backend.body') , array('Attr.EnableID' => true))}}</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th><i class="fas fa-arrows-alt"></i></th>
                            <th><input type="checkbox" id="options1"></th>
                            <th scope="col">{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}}</th>
                            <th scope="col">{{clean( trans('meridian-backend.owner') , array('Attr.EnableID' => true))}}</th>
                            <th scope="col">{{clean( trans('meridian-backend.title') , array('Attr.EnableID' => true))}}</th>
                            <th scope="col">{{clean( trans('meridian-backend.category') , array('Attr.EnableID' => true))}}</th>
                            <th scope="col">{{clean( trans('meridian-backend.body') , array('Attr.EnableID' => true))}}</th>
                        </tr>
                    </tfoot>
                    <tbody id="sortable-projects">
                        @if($projects)
                            @foreach($projects as $project)
                                <tr data-id="{{$project->id}}" class="sortable-row">
                                    <td class="drag-handle" style="cursor: move; text-align: center; vertical-align: middle;">
                                        <i class="fas fa-grip-vertical text-muted"></i>
                                    </td>
                                    <td><input class="checkboxes" type="checkbox" name="checkbox_array[]" value="{{$project->id}}"></td>
                                    <td data-label="Photo">
                                        @if($project->isVideoType())
                                            <div class="position-relative">
                                                <img height="50"
                                                     src="{{ $project->getYoutubeThumbnail('mqdefault') ?: '/img/200x200.png' }}"
                                                     alt="Video Thumbnail"
                                                     class="youtube-thumbnail"
                                                     data-video-id="{{ $project->getYoutubeVideoId() }}"
                                                     onerror="handleYoutubeThumbnailError(this)">
                                                <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 12px;">
                                                    <i class="fas fa-play-circle"></i>
                                                </div>
                                                <small class="d-block text-muted">Video</small>
                                            </div>
                                        @else
                                            <img height="50" src="{{$project->photo ? '/images/media/' . $project->photo->file : '/img/200x200.png'}}" alt="">
                                            <small class="d-block text-muted">Image</small>
                                        @endif
                                        <p class="mb-0 mt-2"><a href="{{ route('project.edit', $project->id) . '?language=' . request()->input('language')}}">{{clean( trans('meridian-backend.edit') , array('Attr.EnableID' => true))}}</a></p>
                                    </td>
                                    <td data-label="OWNER">{{$project->user->name}}</td>
                                    <td data-label="TITLE">{{$project->title}}</td>
                                    <td data-label="Category">{{$project->project_category ? $project->project_category->name : 'Uncategorized'}}</td>
                                    <td class="body-project">{!!$project->body!!}</td>
                                </tr>
                             @endforeach
                        @endif


                        
                    </tbody>
                </table>

                </form>

            </div>
        </div>
    </div>

</div>
<!-- /.container-fluid -->

@stop

@section('styles')
<style>
    .sortable-row {
        transition: all 0.3s ease;
    }

    .sortable-row.sortable-ghost {
        opacity: 0.4;
        background-color: #f8f9fa;
    }

    .sortable-row.sortable-chosen {
        background-color: #e3f2fd;
    }

    .drag-handle:hover {
        background-color: #f8f9fa;
    }

    .sorting-active {
        cursor: grabbing !important;
    }

    #reorder-message.alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    #reorder-message.alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }
</style>
@endsection

@section('footer')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Sortable
    const sortableElement = document.getElementById('sortable-projects');

    if (sortableElement) {
        const sortable = Sortable.create(sortableElement, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onStart: function(evt) {
                $('body').addClass('sorting-active');
            },
            onEnd: function(evt) {
                $('body').removeClass('sorting-active');

                // Get the new order
                const projectIds = [];
                $('#sortable-projects tr').each(function(index) {
                    const projectId = $(this).data('id');
                    if (projectId) {
                        projectIds.push(projectId);
                    }
                });

                // Send AJAX request to update order
                if (projectIds.length > 0) {
                    updateProjectOrder(projectIds);
                }
            }
        });
    }

    function updateProjectOrder(projectIds) {
        $.ajax({
            url: '{{ route("project.reorder") }}',
            method: 'POST',
            data: {
                project_ids: projectIds,
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                showMessage('Updating order...', 'info');
            },
            success: function(response) {
                if (response.success) {
                    showMessage('Project order updated successfully!', 'success');
                } else {
                    showMessage('Failed to update project order', 'danger');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Failed to update project order';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showMessage(errorMessage, 'danger');

                // Reload page to restore original order
                setTimeout(function() {
                    location.reload();
                }, 2000);
            }
        });
    }

    function showMessage(message, type) {
        const messageDiv = $('#reorder-message');
        const messageText = $('#reorder-message-text');

        // Remove existing alert classes
        messageDiv.removeClass('alert-success alert-danger alert-info');

        // Add appropriate class
        messageDiv.addClass('alert-' + type);

        // Set message text
        messageText.text(message);

        // Show message
        messageDiv.show();

        // Auto-hide success messages after 3 seconds
        if (type === 'success') {
            setTimeout(function() {
                messageDiv.fadeOut();
            }, 3000);
        }
    }
});
</script>
@endsection

