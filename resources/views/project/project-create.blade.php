

@extends('layouts.admin')

@section('content')

@include('includes.tinyeditor')

<!-- Begin Page Content -->
<div class="container-fluid">


    <!-- Page Heading -->
    <h1 class="h3 mb-2 text-gray-800">{{clean( trans('meridian-backend.create_project') , array('Attr.EnableID' => true))}}</h1>

    <!-- DataTales Example -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{{clean( trans('meridian-backend.create_project') , array('Attr.EnableID' => true))}}</h6>
        </div>
        <div class="card-body">

                

                <div class="row">
                    <div class="col-lg-6">
                        <a href="{{route('project.index') . '?language=' . request()->input('language')}}" class="btn btn-primary btn-back">{{clean( trans('meridian-backend.back_projectpage') , array('Attr.EnableID' => true))}}</a>
                    </div>

                    <div class="col-lg-6 text-right">
                        @if (!empty($langs))
                            <select name="language" class="form-control language-control" onchange="window.location='{{url()->current() . '?language='}}'+this.value">
                                <option value="" selected disabled>{{clean( trans('meridian-backend.select_language') , array('Attr.EnableID' => true))}}</option>
                                @foreach ($langs as $lang)
                                    <option value="{{$lang->code}}" {{$lang->code == request()->input('language') ? 'selected' : ''}}>{{$lang->name}}</option>
                                @endforeach
                            </select>
                        @endif
                    </div>
                </div>



                @if ($message = Session::get('project_success'))
                    <div class="alert alert-success alert-block">
                        <button type="button" class="close" data-dismiss="alert"><i class="fas fa-times"></i></button>    
                        <strong>{{ $message }}</strong>
                    </div>
                @endif
   

                @include('includes.form-errors')

                <div class="row">
                    <div class="col-md-12">

                        <form action="{{route('project.store')}}" method="POST" enctype="multipart/form-data">
                            @csrf

                            <div class="row">
                                <div class="col-xs-12 col-sm-12 col-md-12">

                      

                                    <input type="hidden" name="language_id" value="{{$lang_id}}">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.title') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="title" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.link') , array('Attr.EnableID' => true))}}</strong>
                                                <div class="slug-container"><span>{{URL::to('/')}}/{{clean( trans('meridian-backend.project') , array('Attr.EnableID' => true))}}/</span><input type="text" name="slug" class="form-control" placeholder=""></div>
                                            </div>
                                        </div>
                                    </div>


                                    <!-- Media Type Selection -->
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <strong>Media Type</strong>
                                                <div class="mt-2">
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio" name="media_type" id="media_type_image" value="image" checked>
                                                        <label class="form-check-label" for="media_type_image">
                                                            Image
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio" name="media_type" id="media_type_video" value="video">
                                                        <label class="form-check-label" for="media_type_video">
                                                            YouTube Video
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Image Upload Section -->
                                    <div id="image_section">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <strong>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}}</strong>
                                                    <input type="file"  name="photo_id" class="form-control-file"  id="photo_id">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <strong>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}} <span>{{clean( trans('meridian-backend.upload_image') , array('Attr.EnableID' => true))}} <a target="_blank" href="{{route('media.create')}}"> {{clean( trans('meridian-backend.here') , array('Attr.EnableID' => true))}} </a> {{clean( trans('meridian-backend.then_copy_url') , array('Attr.EnableID' => true))}} <a target="_blank" href="{{route('media.index')}}"> {{clean( trans('meridian-backend.here') , array('Attr.EnableID' => true))}} </a></span></strong>
                                                    <input type="text" name="image_featured2" class="form-control" placeholder="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- YouTube Video Section -->
                                    <div id="video_section" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <strong>YouTube Video URL</strong>
                                                    <input type="url" name="youtube_video_url" class="form-control" placeholder="https://www.youtube.com/watch?v=..." id="youtube_video_url">
                                                    <small class="form-text text-muted">Enter a valid YouTube URL (e.g., https://www.youtube.com/watch?v=dQw4w9WgXcQ or https://youtu.be/dQw4w9WgXcQ)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" id="video_preview" style="display: none;">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <strong>Video Preview</strong>
                                                    <div class="youtube-preview-container">
                                                        <iframe id="youtube_preview_iframe" width="100%" height="315" frameborder="0" allowfullscreen></iframe>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="form-group">
                                        <strong>{{clean( trans('meridian-backend.categories') , array('Attr.EnableID' => true))}}</strong>
                                        <select name="project_category_id" id="project_category_id" class="form-control">
                                            <option>{{clean( trans('meridian-backend.choose_category') , array('Attr.EnableID' => true))}}</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach 
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <strong>{{clean( trans('meridian-backend.body') , array('Attr.EnableID' => true))}}</strong>
                                        <div id="editor-container" style="height: 300px;"></div>
                                        <textarea name="body" class="form-control" id="body" style="display: none;"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <strong>{{clean( trans('meridian-backend.description') , array('Attr.EnableID' => true))}}</strong>
                                        <textarea name="excerpt" class="form-control" id="excerpt" rows="10"></textarea>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}} 1</strong>
                                                <input type="text" name="img_gal1" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}} 2</strong>
                                                <input type="text" name="img_gal2" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}} 3 </strong>
                                                <input type="text" name="img_gal3" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.photo') , array('Attr.EnableID' => true))}}4 </strong>
                                                <input type="text" name="img_gal4" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.duration_project') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="date" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.client') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="client" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                    </div>

                                     <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.button_text') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="button_text" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.button_link') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="button_link" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                    </div>

                                     <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.meta_title') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="meta_title" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <strong>{{clean( trans('meridian-backend.meta_description') , array('Attr.EnableID' => true))}}</strong>
                                                <input type="text" name="meta_description" class="form-control" placeholder="">
                                            </div>
                                        </div>
                                    </div>

                                </div>
  

                                <div class="col-xs-12 col-sm-12 col-md-12 text-right">
                                    <button type="submit" class="btn btn-primary">{{clean( trans('meridian-backend.create') , array('Attr.EnableID' => true))}}</button>
                                </div>
                            </div>

                        </form>

                    </div>
                </div>

        </div>
    </div>

</div>
<!-- /.container-fluid -->

<!-- Image Editor Modal -->
<div id="image-editor-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div style="background-color: white; margin: 15% auto; padding: 20px; border: 1px solid #888; width: 400px; border-radius: 5px;">
        <h3>Add/Edit Image</h3>
        <div style="margin-bottom: 10px;">
            <label for="image-src">Image URL:</label>
            <input type="text" id="image-src" style="width: 100%; padding: 5px; margin-top: 5px;">
        </div>
        <div style="margin-bottom: 20px;">
            <label for="image-alt">Alt Text:</label>
            <input type="text" id="image-alt" style="width: 100%; padding: 5px; margin-top: 5px;">
        </div>
        <button id="save-image" style="background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 3px; margin-right: 10px;">Save</button>
        <button id="cancel-image" style="background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 3px;">Cancel</button>
    </div>
</div>

<!-- Link Editor Modal -->
<div id="link-editor-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div style="background-color: white; margin: 15% auto; padding: 20px; border: 1px solid #888; width: 400px; border-radius: 5px;">
        <h3>Add Link</h3>
        <div style="margin-bottom: 10px;">
            <label for="link-url">URL:</label>
            <input type="text" id="link-url" style="width: 100%; padding: 5px; margin-top: 5px;">
        </div>
        <div style="margin-bottom: 20px;">
            <label for="link-text">Link Text:</label>
            <input type="text" id="link-text" style="width: 100%; padding: 5px; margin-top: 5px;">
        </div>
        <button id="save-link" style="background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 3px; margin-right: 10px;">Save</button>
        <button id="cancel-link" style="background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 3px;">Cancel</button>
    </div>
</div>

@endsection

@section('scripts')
<script>
// Use both jQuery and vanilla JS for maximum compatibility
document.addEventListener('DOMContentLoaded', function() {
    // Initialize form state based on current media type
    initializeMediaTypeSwitching();
});

// Also use jQuery ready as fallback
$(document).ready(function() {
    initializeMediaTypeSwitching();
});

function initializeMediaTypeSwitching() {
    // Prevent double initialization
    if (window.mediaTypeSwitchingInitialized) {
        return;
    }
    window.mediaTypeSwitchingInitialized = true;

    console.log('Initializing media type switching...');

    // Get current media type
    var currentMediaType = document.querySelector('input[name="media_type"]:checked');
    if (currentMediaType) {
        console.log('Initial media type:', currentMediaType.value);
        toggleMediaSections(currentMediaType.value);
    }

    // Handle media type switching
    var mediaTypeInputs = document.querySelectorAll('input[name="media_type"]');
    mediaTypeInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            console.log('Media type changed to:', this.value);
            toggleMediaSections(this.value);
        });
    });

    // YouTube URL preview
    var youtubeUrlInput = document.getElementById('youtube_video_url');
    if (youtubeUrlInput) {
        youtubeUrlInput.addEventListener('input', function() {
            updateVideoPreview(this.value);
        });
    }
}

function toggleMediaSections(mediaType) {
    console.log('Toggling to media type:', mediaType);

    var imageSection = document.getElementById('image_section');
    var videoSection = document.getElementById('video_section');
    var videoPreview = document.getElementById('video_preview');
    var youtubeUrlInput = document.getElementById('youtube_video_url');

    if (mediaType === 'image') {
        if (imageSection) {
            imageSection.style.display = 'block';
            imageSection.classList.add('active');
        }
        if (videoSection) {
            videoSection.style.display = 'none';
            videoSection.classList.remove('active');
        }
        if (videoPreview) videoPreview.style.display = 'none';
        // Clear video URL when switching to image
        if (youtubeUrlInput) {
            youtubeUrlInput.value = '';
            youtubeUrlInput.classList.remove('valid', 'invalid');
        }
    } else if (mediaType === 'video') {
        if (imageSection) {
            imageSection.style.display = 'none';
            imageSection.classList.remove('active');
        }
        if (videoSection) {
            videoSection.style.display = 'block';
            videoSection.classList.add('active');
        }
        // Trigger preview if URL exists
        if (youtubeUrlInput && youtubeUrlInput.value) {
            updateVideoPreview(youtubeUrlInput.value);
        }
    }
}

function updateVideoPreview(url) {
    var videoId = extractYouTubeVideoId(url);
    var videoPreview = document.getElementById('video_preview');
    var iframe = document.getElementById('youtube_preview_iframe');
    var youtubeUrlInput = document.getElementById('youtube_video_url');

    if (videoId && iframe) {
        var embedUrl = 'https://www.youtube.com/embed/' + videoId;
        iframe.src = embedUrl;
        if (videoPreview) videoPreview.style.display = 'block';
        if (youtubeUrlInput) {
            youtubeUrlInput.classList.remove('invalid');
            youtubeUrlInput.classList.add('valid');
        }
    } else {
        if (videoPreview) videoPreview.style.display = 'none';
        if (youtubeUrlInput && url.trim() !== '') {
            youtubeUrlInput.classList.remove('valid');
            youtubeUrlInput.classList.add('invalid');
        } else if (youtubeUrlInput) {
            youtubeUrlInput.classList.remove('valid', 'invalid');
        }
    }
}

// Function to extract YouTube video ID
function extractYouTubeVideoId(url) {
    if (!url) return false;
    var regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
    var match = url.match(regExp);
    return (match && match[7] && match[7].length == 11) ? match[7] : false;
}
</script>
@endsection

