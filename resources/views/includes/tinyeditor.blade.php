<!-- Quill CSS -->
<link href="{{ asset('css/front/quill.bubble.css') }}" rel="stylesheet">
<link href="{{ asset('css/front/quill.snow.css') }}" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('css/front/default.min.css') }}">

<!-- Quill JS -->
<script src="{{ asset('js/front/quill.min.js') }}"></script>
<script src="{{ asset('js/front/highlight.min.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
  // Initialize Quill editor
  var quill = new Quill('#editor-container', {
    theme: 'snow',
    modules: {
      toolbar: {
        container: [
          ['bold', 'italic', 'underline'],
          ['image'], // Default image button
          [{ 'list': 'ordered' }, { 'list': 'bullet' }],
          ['clean'],
          [{ 'custom-add-image': 'camera' }, { 'custom-add-link': 'link' }] // Custom buttons
        ],
        handlers: {
          'custom-add-image': function () {
            openImageModal();
          },
          'custom-add-link': function () {
            openLinkModal();
          }
        }
      }
    }
  });

  // Global variables
  var currentImage = null;
  var currentLink = null;

  // Synchronize textarea with Quill content
  var textarea = document.getElementById('body');
  if (textarea.value.trim() !== '') {
    quill.root.innerHTML = textarea.value; // Populate Quill only if textarea is not empty
  }

  quill.on('text-change', function () {
    textarea.value = quill.root.innerHTML;
  });

  // Open image modal
  function openImageModal() {
    document.getElementById('image-src').value = '';
    document.getElementById('image-alt').value = '';
    currentImage = null; // Reset current image
    document.getElementById('image-editor-modal').style.display = 'block';
  }

  // Save image
  document.getElementById('save-image').addEventListener('click', function () {
    var src = document.getElementById('image-src').value;
    var alt = document.getElementById('image-alt').value;

    if (src.trim() === '') {
      alert('Please add a valid image URL!');
      return;
    }

    if (currentImage) {
      // Edit existing image
      currentImage.setAttribute('src', src);
      currentImage.setAttribute('alt', alt);
    } else {
      // Add new image
      var range = quill.getSelection(true);
      quill.insertEmbed(range.index, 'image', src);
      quill.formatText(range.index, 1, { alt: alt });
    }

    document.getElementById('image-editor-modal').style.display = 'none';
  });

  // Cancel image modal
  document.getElementById('cancel-image').addEventListener('click', function () {
    document.getElementById('image-editor-modal').style.display = 'none';
  });

  // Detect click on image to edit
  quill.root.addEventListener('click', function (e) {
    if (e.target.tagName === 'IMG') {
      currentImage = e.target;
      document.getElementById('image-src').value = currentImage.getAttribute('src');
      document.getElementById('image-alt').value = currentImage.getAttribute('alt');
      document.getElementById('image-editor-modal').style.display = 'block';
    }
  });

  // Open link modal
  function openLinkModal() {
    document.getElementById('link-url').value = '';
    document.getElementById('link-text').value = '';
    currentLink = null; // Reset current link
    document.getElementById('link-editor-modal').style.display = 'block';
  }

  // Save link
  document.getElementById('save-link').addEventListener('click', function () {
    var url = document.getElementById('link-url').value;
    var text = document.getElementById('link-text').value;

    if (url.trim() === '') {
      alert('Please add a valid URL for the link!');
      return;
    }

    var range = quill.getSelection(true);
    quill.insertText(range.index, text || url, { link: url });

    document.getElementById('link-editor-modal').style.display = 'none';
  });

  // Cancel link modal
  document.getElementById('cancel-link').addEventListener('click', function () {
    document.getElementById('link-editor-modal').style.display = 'none';
  });
});
</script>